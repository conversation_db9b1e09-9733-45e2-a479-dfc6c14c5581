<template>
  <el-select-v2
    v-model="value"
    :options="options"
    :props="props"
    placeholder="Please select"
    style="width: 240px"
    filterable
    multiple
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const initials = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j']
const props = {
  label: 'name',
  value: 'id',
}
const value = ref([])
const options = Array.from({ length: 1000 }).map((_, idx) => ({
  id: `Option ${idx + 1}`,
  name: `${initials[idx % 10]}${idx}`,
}))
</script>
