<script setup lang="ts">
import { computed } from 'vue'
import { inBrowser, useData } from 'vitepress'

// import VPNavbarSearch from './navbar/vp-search.vue'
import VPNavbarMenu from './navbar/vp-menu.vue'
// import VPNavbarThemeToggler from './navbar/vp-theme-toggler.vue'
// import VPNavbarSocialLinks from './navbar/vp-social-links.vue'


defineEmits(['toggle'])

const { theme, page } = useData()

const currentLink = computed(() => {
  if (!inBrowser) {
    return `/${page.value?.frontmatter?.lang || ''}/`
  }

  return '/'
})
</script>

<template>  <div class="navbar-wrapper">
    <div class="header-container">
      <div class="logo-container">
        <a :href="currentLink">
          <img
            class="logo"
            src="/logo.png"
            alt="Element Plus Logo"
          />
          <span class="logo-title">BS NEXUS</span>
        </a>
      </div>
      <div class="content">
        <!-- <VPNavbarSearch class="search" :options="theme.agolia" multilang /> -->
        <VPNavbarMenu class="menu" />
        <!-- <VPNavbarThemeToggler class="theme-toggler" /> -->
        <!-- <VPNavbarSocialLinks class="social-links" /> -->
        <!-- <VPNavbarHamburger
          :active="fullScreen"
          class="hamburger"
          @click="$emit('toggle')"
        /> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.logo-container {
  display: flex;
  align-items: center;
  height: var(--header-height);
  > a {
    display: flex;
    align-items: center;
    height: 28px;
    width: 128px;
  }
  .logo {
    position: relative;
    height: 100%;
  }
}
.logo-title {
  white-space: nowrap;
  padding-left: 10px;
  font-size: 22px;
  font-weight: 700;
  color: var(--text-color);
}
.dark {
  .logo {
    filter: drop-shadow(2px 2px 6px #409eff);
  }
}
</style>
