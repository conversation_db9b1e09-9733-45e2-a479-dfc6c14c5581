<template>
  <bs-switch v-model="showOverflow" active-text="Tooltip" inactive-text="换行" />
  <bs-table-pro :show-overflow="showOverflow" :columns="tableColumns" :data="tableData" />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

const showOverflow = ref(true)
const tableColumns = computed(() => {
  return [
    {
      label: '姓名',
      value: 'name',
      with: 180
    },
    {
      label: '年龄',
      value: 'age',
      width: 80
    },
    {
      label: '日期',
      value: 'date',
      with: 180
    },
    {
      label: '地址地址地址地址地址地址地址地址地址地址地址地址地址',
      value: 'address'
    }
  ]
})
const tableData = [
  {
    id: 1,
    date: '2016-05-02 2016-05-02 2016-05-02 2016-05-02',
    name: '王小虎',
    age: 18,
    address: '中华人民共和国上海市普陀区金沙江路 1517 弄翻斗花园街道17号翻斗花园17栋17单元1701'
  },
  {
    id: 2,
    date: '2016-05-04 2016-05-04 2016-05-04 2016-05-04',
    name: '王小龙',
    age: 19,
    address: '中华人民共和国上海市普陀区金沙江路 1518 弄翻斗花园街道18号翻斗花园18栋18单元1801'
  },
  {
    id: 3,
    date: '2016-05-01 2016-05-01 2016-05-01 2016-05-01',
    name: '王小二',
    age: 20,
    address: '中华人民共和国上海市普陀区金沙江路 1519 弄翻斗花园街道19号翻斗花园19栋19单元1901'
  },
  {
    id: 4,
    date: '2016-05-03 2016-05-03 2016-05-03 2016-05-03',
    name: '王大锤',
    age: 21,
    address: '中华人民共和国上海市普陀区金沙江路 1520 弄翻斗花园街道20号翻斗花园20栋20单元2001'
  }
]
</script>
