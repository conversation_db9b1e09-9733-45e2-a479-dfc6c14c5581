---
title: Table 表格
lang: zh-CN
---

# Table 表格

用于展示多条结构类似的数据， 可对数据进行排序、筛选、对比或其他自定义操作。

## 基础用法

基础的表格展示用法。

:::demo 当 bs-table 元素中注入 `data` 对象数组后，在 `columns` 中用 `value` 属性来对应对象中的键名即可填入数据，用 `label` 属性来定义表格的列名。 可以使用 `width` 属性来定义列宽

table-pro/basic

:::
·

## 带斑马纹表格

使用带斑马纹的表格，可以更容易区分出不同行的数据。

:::demo `stripe` 可以创建带斑马纹的表格。 如果 `true`, 表格将会带有斑马纹。

table-pro/stripe

:::

## 带边框表格 ​

:::demo 默认情况下，Table 组件是不具有竖直方向的边框的， 如果需要，可以使用 `border` 属性，把该属性设置为 `true` 即可启用

table-pro/border

:::

## 自定义左上角操作栏 ​

:::demo 可以通过`operations` 属性或 `operations`插槽完成对左上角操作栏的定义

table-pro/operations

:::

## 隐藏操作栏 ​

:::demo 可以通过把`showToolbar`属性设置为 `false` 来隐藏操作栏

table-pro/show-toolbar

:::

## 内容换行的表格

默认情况下。当内容过长时多余的内容会在 hover 时以 tooltip 的形式显示出来。可以通过把`showOverflow`属性设置为 `false` 让内容进行多行显示。

:::demo

table-pro/show-overflow

:::

## 固定表头 ​

纵向内容过多时，可选择固定表头。

:::demo 只要在 bs-table 元素中定义了 `height` 属性，即可实现固定表头的表格，而不需要额外的代码。

table-pro/height

:::

## 固定列 ​

横向内容过多时，可选择固定列。

:::demo 固定列需要使用 `fixed` 属性，它接受传入字符串，`left` 或 `right`，表示左边固定还是右边固定。
table-pro/fixed

:::

## 流体高度 ​

当数据量动态变化时，可以为 Table 设置一个最大高度。

:::demo 通过设置 `max-height` 属性为 bs-table 指定最大高度。 此时若表格所需的高度大于最大高度，则会显示一个滚动条。

table-pro/max-height

:::

## 多级表头 ​

数据结构比较复杂的时候，可使用多级表头来展现数据的层次关系。

:::demo 通过配置 `columns` 的`children` 数据，就可以实现表头分组。

table-pro/children

:::

## 单选 ​

选择单行数据时使用色块表示。

:::demo Table 组件提供了单选的支持， 只需要配置 `highlight-current-row` 属性和`row-key`即可实现单选。 之后由 current-change 事件来管理选中时触发的事件，它会传入 currentRow，oldCurrentRow。 ​

table-pro/choice

:::

## 单选 ​radio

选择单行数据时使用色块表示。

:::demo Table 组件提供了单选的支持， 只需要配置 highlight-current-row 属性即可实现单选。 之后由 current-change 事件来管理选中时触发的事件，它会传入 currentRow，oldCurrentRow。 如果需要显示索引，可以增加一列 el-table-column，设置 type 属性为 index 即可显示从 1 开始的索引号。 ​

table-pro/type-radio

:::

## 分页 ​

:::demo 通过配置`pageData`属性，，即可开启分页功能

table-pro/page-data

:::

## 前端分页 ​

:::demo 通过设置`pagingFront`属性为`true`，即可开启前端分页功能，可自动进行前端分页 ​

table-pro/paging-front

:::

## 多选 ​

你也可以选择多行。

实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；

:::demo 实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；。 ​

table-pro/type-checkbox

:::

## 分页 + 跨页多选

你也可以选择多行。

实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；

:::demo 实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；。 ​

table-pro/type-checkbox-page-data

:::

## 前端分页 + 跨页多选 ​

你也可以选择多行。

实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；

:::demo 实现多选非常简单: 手动添加一个 el-table-column，设 type 属性为 selection 即可；。 ​

table-pro/type-checkbox-paging-front

:::

## 序号 ​

对表格进行排序，可快速查找或对比数据。

:::demo 在列中设置`sortable`属性为`true`即可实现排序。可以通过列属性`sortBy`属性或 Table 的`sort-method`属性使用自定义的排序规则完成排序。 ​

table-pro/sortable

:::

## 排序 ​

对表格进行排序，可快速查找或对比数据。

:::demo 在列中设置`sortable`属性为`true`即可实现排序。可以通过列属性`sortBy`属性或 Table 的`sort-method`属性使用自定义的排序规则完成排序。 ​

table-pro/sortable

:::

## 后端排序 ​

对表格进行后端排序。

:::demo 通过设置`sort-remote`属性为`true`，配合`sort-change`事件即可完成后端排序。 ​

table-pro/sort-remote

:::

## 筛选 ​

对表格进行筛选，可快速查找到自己想看的数据。

:::demo 在列中设置 `filters` 该列的筛选，默认多选筛选。可以通过列属性`filterMultiple`属性设置为`false`成为单选筛选。也可以通过列属性`filterMethod`属性或 Table 的`filter-method`属性使用自定义的筛选规则完成筛选。

table-pro/filter

:::

## 后端筛选 ​

对表格进行后端筛选。

:::demo 通过设置`filter-remote`属性为`true`，配合`filter-change`事件即可完成后端筛选。

table-pro/filter-remote

:::

## 虚拟

当行内容过多并且不想显示横向滚动条时，可以使用 Table 展开行功能。

:::demo 通过设置 type="expand" 和 slot 可以开启展开行功能， el-table-column 的模板会被渲染成为展开行的内容，展开行可访问的属性与使用自定义列模板时的 slot 相同。​

table-pro/border
:::

## 展开行

当行内容过多并且不想显示横向滚动条时，可以使用 Table 展开行功能。

:::demo 通过设置 type="expand" 和 slot 可以开启展开行功能， el-table-column 的模板会被渲染成为展开行的内容，展开行可访问的属性与使用自定义列模板时的 slot 相同。​

table-pro/border
:::

## 树形数据与懒加载 ​

:::demo 支持树类型的数据的显示。 当 row 中包含 children 字段时，被视为树形数据。 渲染嵌套数据需要 prop 的 row-key。 此外，子行数据可以异步加载。 设置 Table 的 lazy 属性为 true 与加载函数 load 。 通过指定 row 中的 hasChildren 字段来指定哪些行是包含子节点。 children 与 hasChildren 都可以通过 tree-props 配置

table-pro/border

:::

## 自定义列模板 ​

自定义列的显示内容，可组合其他组件使用。
​
:::demo 通过 slot 可以获取到 row, column, $index 和 store（table 内部的状态管理）的数据，用法参考 demo。

table-pro/border

:::

## 自定义表头 ​

表头支持自定义。

:::demo 通过设置 slot 来自定义表头。

table-pro/border

:::

## 带状态表格 ​

可将表格内容 highlight 显示，方便区分「成功、信息、警告、危险」等内容。

可以通过指定 Table 组件的 `row-class-name` 属性来为 Table 中的某一行添加 class， 这样就可以自定义每一行的样式了。

可以通过指定 Table 组件的 `cell-class-name` 属性来为 Table 中的某一单元格添加 class， 这样就可以自定义每一单元格的样式了。

可以通过指定 Table 组件的 `header-row-class-name` 属性来为 Table 中 Header 的某一行添加 class， 这样就可以自定义每一行的样式了。

可以通过指定 Table 组件的 `header-cell-class-name` 属性来为 Table 中 Header 的某一单元格添加 class， 这样就可以自定义每一单元格的样式了。

可以通过指定 Table 组件的 `footer-row-class-name` 属性来为 Table 中 Footer 的某一行添加 class， 这样就可以自定义每一行的样式了。

可以通过指定 Table 组件的 `footer-cell-class-name` 属性来为 Table 中 Footer 的某一行添加 class， 这样就可以自定义每一单元格的样式了。

:::demo 可以通过指定 Table 组件的 `row-class-name` 属性来为 Table 中的某一行添加 class， 这样就可以自定义每一行的样式了。可以通过指定 Table 组件的 `cell-class-name` 属性来为 Table 中的某一单元格添加 class， 这样就可以自定义每一单元格的样式了。可以通过指定 Table 组件的 `header-row-class-name` 属性来为 Table 中 Header 的某一行添加 class， 这样就可以自定义每一行的样式了。可以通过指定 Table 组件的 `header-cell-class-name` 属性来为 Table 中 Header 的某一单元格添加 class， 这样就可以自定义每一单元格的样式了。可以通过指定 Table 组件的 `footer-row-class-name` 属性来为 Table 中 Footer 的某一行添加 class， 这样就可以自定义每一行的样式了。可以通过指定 Table 组件的 `footer-cell-class-name` 属性来为 Table 中 Footer 的某一行添加 class， 这样就可以自定义每一单元格的样式了。
table-pro/border
:::

## API

### 属性

| 名称     | 说明                   | 类型                       | 默认值 |
| -------- | ---------------------- | -------------------------- | ------ |
| offset   | 偏移距离               | ^[number]                  | 0      |
| position | 表格位置               | ^[enum]`'top' \| 'bottom'` | top    |
| target   | 指定容器（CSS 选择器） | ^[string]                  | —      |
| z-index  | `z-index`              | ^[number]                  | 100    |

### 事件

| 名称               | 说明                                     | 类型                                                                |
| ------------------ | ---------------------------------------- | ------------------------------------------------------------------- |
| refresh            | 点击刷新图标时触发的事件                 | ^[Function]`(fixed: boolean) => void`                               |
| sort-change        | 排序条件变化时触发的事件                 | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| filter-change      | 过滤条件变化时触发的事件                 | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| page-change        | 分页变化事件                             | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| cell-click         | cell 单元格点击时触发的事件              | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| cell-dblclick      | cell 单元格双击时触发的事件              | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| row-click          | row 行点击时触发的事件                   | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| row-dblclick       | row 行双击时触发的事件                   | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| header-click       | header 点击时触发的事件                  | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| header-dblclick    | header 双击时触发的事件                  | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| footer-click       | footer 点击时触发的事件                  | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| footer-dblclick    | footer 双击时触发的事件                  | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| current-change     | 当表格的当前行发生变化的时候会触发该事件 | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| row-expand-change  | 某一行展开或者关闭的时候会触发该事件     | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| tree-expand-change | tree 展开或者关闭的时候会触发该事件      | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| toolbar-click      | toolbar 点击时触发的事件                 | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| scroll             | 表格滚动时会触发该事件                   | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| scroll-boundary    | 当滚动条滚动到边界时会触发该事件         | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| checkbox-change    | 当手动勾选并且值发生改变时触发的事件     | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| checkbox-all       | 当手动勾选全选时触发的事件               | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |
| radio-change       | 当手动勾选并且值发生改变时触发的事件     | ^[Function]`(value: { scrollTop: number, fixed: boolean }) => void` |

### 插槽

| 插槽名     | 说明                   |
| ---------- | ---------------------- |
| empty      | 自定义空数据时显示模板 |
| toolbar    | 工具栏模板             |
| operations | 工具栏左侧操作栏模板   |
| left       | 表格左侧模板           |
| right      | 表格右侧模板           |
| pager      | 分页模板               |
| pagerLeft  | 分页左侧模版           |

### 暴露

| 名称               | 说明                                                     | 类型                    |
| ------------------ | -------------------------------------------------------- | ----------------------- |
| loadData           | 加载数据                                                 | ^[Function]`() => void` |
| reloadRow          | 修改行数据并恢复到初始状态                               | ^[Function]`() => void` |
| getCurrentRecord   | 获取高亮的当前行数据                                     | ^[Function]`() => void` |
| setCurrentRow      | 设置指定行为高亮状态                                     | ^[Function]`() => void` |
| clearCurrentRow    | 手动清空当前高亮的状态                                   | ^[Function]`() => void` |
| getRadioRecord     | type=radio，设置指定行为选中状态                         | ^[Function]`() => void` |
| setRadioRow        | type=radio，手动清空用户的选择                           | ^[Function]`() => void` |
| clearRadioRow      | type=radio，手动清空用户的选择                           | ^[Function]`() => void` |
| getCheckboxRecords | type=checkbox，获取当前已选中的行数据                    | ^[Function]`() => void` |
| setCheckboxRow     | type=checkbox，设置行为选中状态，第二个参数为选中与否    | ^[Function]`() => void` |
| clearCheckboxRow   | type=checkbox，手动清空用户的选择                        | ^[Function]`() => void` |
| setRowExpand       | type=expand 手动清空展开行状态，数据会恢复成未展开的状态 | ^[Function]`() => void` |
| clearRowExpand     | type=expand 手动清空展开行状态，数据会恢复成未展开的状态 | ^[Function]`() => void` |
