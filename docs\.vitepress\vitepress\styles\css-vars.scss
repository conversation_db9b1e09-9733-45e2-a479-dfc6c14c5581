@use './mixins' as *;

// css variables
:root {
  // layouts
  --vp-screen-max-width: 1376px;

  // colors
  --text-color: var(--el-text-color-primary);
  --text-color-light: var(--el-text-color-regular);
  --text-color-lighter: var(--el-text-color-secondary);

  --brand-color: var(--el-color-primary);
  --brand-color-light: var(--el-color-primary-light-1);
  --bg-brand-color: var(--el-color-primary-light-9);

  --bg-color: var(--el-bg-color);
  --bg-color-rgb: 255, 255, 255;
  --bg-color-soft: #fafafa;
  --bg-color-mute: #f2f2f2;
  --border-color: var(--el-border-color);
  --border-color-light: var(--el-border-color-lighter);

  --font-family: var(--el-font-family);
  --font-family-mono: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas,
    'Courier New', monospace;

  // info
  --success-color: var(--el-color-success);
  --warning-color: var(--el-color-warning);
  --danger-color: var(--el-color-danger);
  --purple-color: #6222c2;
  --purple-color-light: #9065db;

  // header vars
  --header-height: 55px;
  --nav-height: 55px; // alias of --header-height

  /* Screen Size */
  --vp-screen-max-width: 1362px;

  // sidebar
  --vp-sidebar-width-mobile: 320px;
  --vp-sidebar-width-small: 266px;

  --sidebar-width-sm: 16rem;
  --sidebar-width-xs: 20rem;
  --content-min-width: 16rem;
  --content-max-width: 48rem;

  --nav-z-index: 12;
  --sub-nav-z-index: 11;
  --sidebar-z-index: 11;
  --sidebar-z-index-mobile: 31;
  --overlay-z-index: 30;
  // --dropdown-z-index: 22;

  // code block vars
  --code-line-height: 1.4;
  --code-font-size: var(--el-font-size-base);
  --code-bg-color: var(--el-fill-color-light);
  --code-text-color: var(--text-color);
  --code-font-family: var(--font-family-mono);
  --code-tooltip-bg-color: var(--code-bg-color);
  --code-tooltip-color: #0c61c9;

  // tip block
  --block-tip-bg-color: rgba(var(--el-color-primary-rgb), 0.1);
  --block-warning-bg-color: rgba(var(--el-color-danger-rgb), 0.1);

  // link
  --link-active-bg-color: rgba(var(--el-color-primary-rgb), 0.1);

  @include respond-to('xxl') {
    --vp-sidebar-width-small: 234px;
  }

  @include respond-to('max') {
    --vp-screen-max-width: 1482px;
    --vp-sidebar-width-small: 290px;
  }
}

.dark {
  --bg-color-rgb: 0, 0, 0;
  --bg-color-soft: #242424;
  --bg-color-mute: #2c2c2c;
  --code-tooltip-bg-color: rgba(var(--el-color-primary-rgb), 0.1);
  --code-tooltip-color: var(--brand-color);
  --purple-color: #9065db;
  --purple-color-light: #6222c2;
}
