declare module 'vue' {
  // GlobalComponents for Volar
  export interface GlobalComponents {
    BsAffix: typeof import('bs-nexus')['BsAffix']
    BsAlert: typeof import('bs-nexus')['BsAlert']
    BsAside: typeof import('bs-nexus')['BsAside']
    BsAutoResizer: typeof import('bs-nexus')['BsAutoResizer']
    BsAutocomplete: typeof import('bs-nexus')['BsAutocomplete']
    BsAvatar: typeof import('bs-nexus')['BsAvatar']
    BsAnchor: typeof import('bs-nexus')['BsAnchor']
    BsAnchorLink: typeof import('bs-nexus')['BsAnchorLink']
    BsBacktop: typeof import('bs-nexus')['BsBacktop']
    BsBadge: typeof import('bs-nexus')['BsBadge']
    BsBreadcrumb: typeof import('bs-nexus')['BsBreadcrumb']
    BsBreadcrumbItem: typeof import('bs-nexus')['BsBreadcrumbItem']
    BsButton: typeof import('bs-nexus')['BsButton']
    BsButtonGroup: typeof import('bs-nexus')['BsButtonGroup']
    BsCalendar: typeof import('bs-nexus')['BsCalendar']
    BsCard: typeof import('bs-nexus')['BsCard']
    BsCarousel: typeof import('bs-nexus')['BsCarousel']
    BsCarouselItem: typeof import('bs-nexus')['BsCarouselItem']
    BsCascader: typeof import('bs-nexus')['BsCascader']
    BsCascaderPanel: typeof import('bs-nexus')['BsCascaderPanel']
    BsCheckbox: typeof import('bs-nexus')['BsCheckbox']
    BsCheckboxButton: typeof import('bs-nexus')['BsCheckboxButton']
    BsCheckboxGroup: typeof import('bs-nexus')['BsCheckboxGroup']
    BsCol: typeof import('bs-nexus')['BsCol']
    BsCollapse: typeof import('bs-nexus')['BsCollapse']
    BsCollapseItem: typeof import('bs-nexus')['BsCollapseItem']
    BsCollapseTransition: typeof import('bs-nexus')['BsCollapseTransition']
    BsColorPicker: typeof import('bs-nexus')['BsColorPicker']
    BsContainer: typeof import('bs-nexus')['BsContainer']
    BsConfigProvider: typeof import('bs-nexus')['BsConfigProvider']
    BsDatePicker: typeof import('bs-nexus')['BsDatePicker']
    BsDialog: typeof import('bs-nexus')['BsDialog']
    BsDivider: typeof import('bs-nexus')['BsDivider']
    BsDrawer: typeof import('bs-nexus')['BsDrawer']
    BsDropdown: typeof import('bs-nexus')['BsDropdown']
    BsDropdownItem: typeof import('bs-nexus')['BsDropdownItem']
    BsDropdownMenu: typeof import('bs-nexus')['BsDropdownMenu']
    BsEmpty: typeof import('bs-nexus')['BsEmpty']
    BsFooter: typeof import('bs-nexus')['BsFooter']
    BsForm: typeof import('bs-nexus')['BsForm']
    BsFormItem: typeof import('bs-nexus')['BsFormItem']
    BsHeader: typeof import('bs-nexus')['BsHeader']
    BsIcon: typeof import('bs-nexus')['BsIcon']
    BsImage: typeof import('bs-nexus')['BsImage']
    BsImageViewer: typeof import('bs-nexus')['BsImageViewer']
    BsInput: typeof import('bs-nexus')['BsInput']
    BsInputNumber: typeof import('bs-nexus')['BsInputNumber']
    BsInputTag: typeof import('bs-nexus')['BsInputTag']
    BsLink: typeof import('bs-nexus')['BsLink']
    BsMain: typeof import('bs-nexus')['BsMain']
    BsMenu: typeof import('bs-nexus')['BsMenu']
    BsMenuItem: typeof import('bs-nexus')['BsMenuItem']
    BsMenuItemGroup: typeof import('bs-nexus')['BsMenuItemGroup']
    BsOption: typeof import('bs-nexus')['BsOption']
    BsOptionGroup: typeof import('bs-nexus')['BsOptionGroup']
    BsPageHeader: typeof import('bs-nexus')['BsPageHeader']
    BsPagination: typeof import('bs-nexus')['BsPagination']
    BsPopconfirm: typeof import('bs-nexus')['BsPopconfirm']
    BsPopper: typeof import('bs-nexus')['BsPopper']
    BsPopover: typeof import('bs-nexus')['BsPopover']
    BsProgress: typeof import('bs-nexus')['BsProgress']
    BsRadio: typeof import('bs-nexus')['BsRadio']
    BsRadioButton: typeof import('bs-nexus')['BsRadioButton']
    BsRadioGroup: typeof import('bs-nexus')['BsRadioGroup']
    BsRate: typeof import('bs-nexus')['BsRate']
    BsRow: typeof import('bs-nexus')['BsRow']
    BsScrollbar: typeof import('bs-nexus')['BsScrollbar']
    BsSelect: typeof import('bs-nexus')['BsSelect']
    BsSlider: typeof import('bs-nexus')['BsSlider']
    BsStep: typeof import('bs-nexus')['BsStep']
    BsSteps: typeof import('bs-nexus')['BsSteps']
    BsSubMenu: typeof import('bs-nexus')['BsSubMenu']
    BsSwitch: typeof import('bs-nexus')['BsSwitch']
    BsTabPane: typeof import('bs-nexus')['BsTabPane']
    BsTable: typeof import('bs-nexus')['BsTable']
    BsTableV2: typeof import('bs-nexus')['BsTableV2']
    BsTableColumn: typeof import('bs-nexus')['BsTableColumn']
    BsTabs: typeof import('bs-nexus')['BsTabs']
    BsTag: typeof import('bs-nexus')['BsTag']
    BsText: typeof import('bs-nexus')['BsText']
    BsTimePicker: typeof import('bs-nexus')['BsTimePicker']
    BsTimeSelect: typeof import('bs-nexus')['BsTimeSelect']
    BsTimeline: typeof import('bs-nexus')['BsTimeline']
    BsTimelineItem: typeof import('bs-nexus')['BsTimelineItem']
    BsTooltip: typeof import('bs-nexus')['BsTooltip']
    BsTransfer: typeof import('bs-nexus')['BsTransfer']
    BsTree: typeof import('bs-nexus')['BsTree']
    BsTreeV2: typeof import('bs-nexus')['BsTreeV2']
    BsTreeSelect: typeof import('bs-nexus')['BsTreeSelect']
    BsUpload: typeof import('bs-nexus')['BsUpload']
    BsSpace: typeof import('bs-nexus')['BsSpace']
    BsSkeleton: typeof import('bs-nexus')['BsSkeleton']
    BsSkeletonItem: typeof import('bs-nexus')['BsSkeletonItem']
    BsStatistic: typeof import('bs-nexus')['BsStatistic']
    BsCountdown: typeof import('bs-nexus')['BsCountdown']
    BsCheckTag: typeof import('bs-nexus')['BsCheckTag']
    BsDescriptions: typeof import('bs-nexus')['BsDescriptions']
    BsDescriptionsItem: typeof import('bs-nexus')['BsDescriptionsItem']
    BsResult: typeof import('bs-nexus')['BsResult']
    BsSelectV2: typeof import('bs-nexus')['BsSelectV2']
    BsWatermark: typeof import('bs-nexus')['BsWatermark']
    BsTour: typeof import('bs-nexus')['BsTour']
    BsTourStep: typeof import('bs-nexus')['BsTourStep']
    BsSegmented: typeof import('bs-nexus')['BsSegmented']
    BsMention: typeof import('bs-nexus')['BsMention']
    BsSplitter: typeof import('bs-nexus')['BsSplitter']
    BsSplitterPanel: typeof import('bs-nexus')['BsSplitterPanel']
    BsSelect: typeof import('bs-nexus')['BsSelect']
    BsDialog: typeof import('bs-nexus')['BsDialog']
    BsSearch: typeof import('bs-nexus')['BsSearch']
  }

  interface ComponentCustomProperties {
    $message: typeof import('bs-nexus')['BsMessage']
    $notify: typeof import('bs-nexus')['BsNotification']
    $msgbox: typeof import('bs-nexus')['BsMessageBox']
    $messageBox: typeof import('bs-nexus')['BsMessageBox']
    $alert: typeof import('bs-nexus')['BsMessageBox']['alert']
    $confirm: typeof import('bs-nexus')['BsMessageBox']['confirm']
    $prompt: typeof import('bs-nexus')['BsMessageBox']['prompt']
    $loading: typeof import('bs-nexus')['BsLoadingService']
  }
}

export {}
