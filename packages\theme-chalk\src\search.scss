@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(search) {
  display: inline-block;
  position: relative;

  @include e(icon) {
    cursor: pointer;
    color: getCssVar('text-color', 'placeholder');

    &:hover {
      color: getCssVar('color', 'primary');
    }
  }

  @include e(select) {
    .#{$namespace}-dropdown {
      height: 100%;
    }
  }

  @include e(select-target) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 8px;
    background-color: getCssVar('fill-color', 'blank');
    border: 1px solid getCssVar('border-color');
    border-right: none;
    border-radius: getCssVar('border-radius-base') 0 0 getCssVar('border-radius-base');
    cursor: pointer;
    transition: all getCssVar('transition-duration');
    min-width: 80px;

    &:hover {
      border-color: getCssVar('border-color-hover');
    }
  }

  @include e(select-active-target) {
    border-color: getCssVar('color', 'primary');
  }

  @include e(select-text) {
    font-size: getCssVar('font-size', 'base');
    color: getCssVar('text-color', 'regular');
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60px;

    &.bs-search-no-text {
      color: getCssVar('text-color', 'placeholder');
    }
  }

  @include e(select-icon) {
    margin-left: 4px;
    transition: transform getCssVar('transition-duration');
    color: getCssVar('text-color', 'placeholder');

    &.is-reverse {
      transform: rotate(180deg);
    }
  }

  @include e(select-item) {
    &.bs-search__select-item-active {
      color: getCssVar('color', 'primary');
      background-color: getCssVar('color', 'primary', 'light-9');
    }
  }

  // 修复输入框与选择器的连接
  .#{$namespace}-input-group__prepend {
    padding: 0;
    background-color: transparent;
    border: none;
  }

  .#{$namespace}-autocomplete {
    width: 100%;
  }

  .#{$namespace}-input {
    width: 100%;
  }
}
