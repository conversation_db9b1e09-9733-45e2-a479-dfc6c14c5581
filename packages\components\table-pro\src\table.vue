<template>
  <div ref="root" :class="ns.b()">
    <bs-grid ref="gridRef" v-bind="gridProps" v-on="gridEvents">
      <template v-for="(_, name) in $slots" #[name]="slotData" :key="name">
        <slot :name="name" v-bind="slotData || {}" />
      </template>
    </bs-grid>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useNamespace } from '@bs-nexus/hooks'
import { tableEmits, tableProps } from './table'
import BsGrid from './bs-grid'
import { handleColumns, isFunction } from './utils'

defineOptions({ name: 'BsTablePro' })
const props = defineProps(tableProps)
const emit = defineEmits(tableEmits)
const gridRef = ref<any>()
const ns = useNamespace('table')

const tableData = computed(() => {
  if (!props.pagingFront || !props.pageData?.currentPage || !props.pageData?.pageSize) return props.data
  return props.data.slice((props.pageData.currentPage - 1) * props.pageData.pageSize, props.pageData.currentPage * props.pageData.pageSize)
})

/* Grid Props */
const gridProps = computed(() => {
  return {
    padding: true,
    data: tableData.value,
    columns: handleColumns(props.columns, props),
    border: props.border ? 'full' : 'default',
    stripe: props.stripe,
    operations: props.operations || [],
    showToolbar: props.showToolbar,
    showOverflow: props.showOverflow ? 'tooltip' : false,
    columnConfig: {
      resizable: props.resizable
    },
    height: props.height, // 表格高度 TODO 现在还是表格本身的高度，需要转换成包含分页和toolbar的高度
    minHeight: props.minHeight, // 表格最小高度 待定, 后续可删除 TODO 现在还是表格本身的高度，需要转换成包含分页和toolbar的高度
    maxHeight: props.maxHeight, // TODO 现在还是表格本身的高度，需要转换成包含分页和toolbar的高度
    autoResize: true,
    syncResize: props.syncResize,
    loading: props.loading,
    showHeader: props.showHeader,
    showFooter: props.showFooter,
    footerData: props.footerData,
    align: props.align,
    headerAlign: props.align,
    footerAlign: props.align,
    rowClassName: props.rowClassName, // row增加className
    cellClassName: props.cellClassName, // cell增加className
    headerRowClassName: props.headerRowClassName, // header的row增加className
    headerCellClassName: props.headerCellClassName, // header的cell增加className
    footerRowClassName: props.footerRowClassName, // footer的row增加className
    footerCellClassName: props.footerCellClassName, // footer的cell增加className
    crossing: props.crossing, // 开启跨页选择
    mergeCells: props.mergeCells,
    rowConfig: {
      useKey: true,
      keyField: props.rowKey,
      isCurrent: props.highlightCurrentRow,
      isHover: true
    },
    seqConfig: {
      ...(isFunction(props.indexMethod) && { seqMethod: props.indexMethod })
    },
    sortConfig: {
      remote: props.sortRemote,
      ...(isFunction(props.sortMethod) && { sortMethod: props.sortMethod })
    },
    filterConfig: {
      remote: props.filterRemote,
      ...(isFunction(props.filterMethod) && { filterMethod: props.filterMethod })
    },
    radioConfig: {},
    checkboxConfig: {},
    expandConfig: {},
    emptyText: props.emptyText,
    loadingConfig: {
      text: props.loadingText
    },
    virtualXConfig: {
      enabled: props.autoVirtual,
      gt: props.autoVirtual,
      threshold: 20
    },
    virtualYConfig: {
      enabled: props.autoVirtual,
      gt: props.autoVirtual,
      threshold: 20
    },
    scrollbarConfig: {
      // width
      // height
      // x: {
      //   position: ''
      // },
      // y: {
      //   position: ''
      // }
    },
    ...(props.pageData && { pageData: props.pageData }),
    ...(props.treeConfig && { treeConfig: props.treeConfig })
  }
})
// console.log('🚀 ~ gridProps:', gridProps.value)
/* Grid Event */
const gridEvents = {
  refresh: () => emit('refresh'), // refresh事件
  'sort-change': (raw: any) => {
    emit('sort-change', {
      column: raw.column,
      field: raw.field,
      order: raw.order,
      sortBy: raw.sortBy,
      sortList: raw.sortList
    })
  }, // 用于多选表格，切换某一行的选中状态，如果使用了第二个参数，则是设置这一行选中与否（selected 为 true 则选中
  'filter-change': (raw: any) => {
    emit('filter-change', {
      column: raw.column,
      field: raw.field,
      values: raw.values,
      datas: raw.datas,
      filterList: raw.filterList
    }) // 用于多选表格，切换所有行的选中状态
  },
  'page-change': (currentPage: number, pageSize: number) => emit('page-change', currentPage, pageSize), // 分页变化事件
  'cell-click': (...args: any[]) => emit('cell-click', ...args), // 单元格点击事件
  'cell-dblclick': (...args: any[]) => emit('cell-dblclick', ...args), // 单元格双击事件
  'row-click': (...args: any[]) => emit('row-click', ...args), //row点击事件
  'row-dblclick': (...args: any[]) => emit('row-dblclick', ...args), //row双击事件
  'header-click': (...args: any[]) => emit('header-click', ...args), //header点击事件
  'header-dblclick': (...args: any[]) => emit('header-dblclick', ...args), //header双击事件
  'footer-click': (...args: any[]) => emit('footer-click', ...args), //footer点击事件
  'footer-dblclick': (...args: any[]) => emit('footer-dblclick', ...args), //footer双击事件
  'current-row-change': (raw: any) => emit('current-change', raw.newValue, raw.oldValue), //当表格的当前行发生变化的时候会触发该事件，如果要高亮当前行，请打开表格的 highlight-current-row 属性
  'row-expand-change': (...args: any[]) => emit('row-expand-change', ...args), //某一行展开或者关闭的时候会触发该事件
  'tree-expand-change': (...args: any[]) => emit('tree-expand-change', ...args), //tree 展开或者关闭的时候会触发该事件
  'toolbar-click': (...args: any[]) => emit('toolbar-click', ...args), //toolbar点击事件
  scroll: (raw: any) => emit('scroll', raw), //表格滚动时会触发该事件
  'scroll-boundary': (...args: any[]) => emit('scroll-boundary', ...args), // 当滚动条滚动到边界时会触发该事件
  'checkbox-change': (raw: any) => emit('checkbox-change', raw.records), // 当手动勾选并且值发生改变时触发的事件
  'checkbox-all': (raw: any) => emit('checkbox-all', raw.records), // 当手动勾选全选时触发的事件
  'radio-change': (raw: any) => emit('radio-change', raw.newValue, raw.oldValue) // 当手动勾选并且值发生改变时触发的事件
}
/* Methods */
defineExpose({
  loadData: (data: any[]) => gridRef.value?.loadData(data), // 加载数据
  reloadRow: () => gridRef.value?.reloadRow(), // 修改行数据并恢复到初始状态
  getCurrentRecord: () => gridRef.value?.getCurrentRecord(), // 获取高亮的当前行数据
  setCurrentRow: (row: any) => gridRef.value?.setCurrentRow(row), // 设置指定行为高亮状态
  clearCurrentRow: () => gridRef.value?.clearCurrentRow(), // 手动清空当前高亮的状态
  getRadioRecord: () => gridRef.value?.getRadioRecord(), // type=radio，获取当前已选中的行数据
  setRadioRow: () => gridRef.value?.setRadioRow(), // type=radio，设置指定行为选中状态
  clearRadioRow: () => gridRef.value?.clearRadioRow(), // type=radio，手动清空用户的选择
  getCheckboxRecords: () => gridRef.value?.getCheckboxRecords(), // type=checkbox，获取当前已选中的行数据
  setCheckboxRow: (rows: any | any[], checked: boolean) => gridRef.value?.setCheckboxRow(rows, checked), // type=checkbox，设置行为选中状态，第二个参数为选中与否
  clearCheckboxRow: () => gridRef.value?.clearCheckboxRow(), // type=checkbox，手动清空用户的选择
  setRowExpand: (rows: any | any[], expanded: boolean) => gridRef.value?.setRowExpand(rows, expanded), // type=expand 手动清空展开行状态，数据会恢复成未展开的状态
  clearRowExpand: () => gridRef.value?.clearRowExpand() // type=expand 手动清空展开行状态，数据会恢复成未展开的状态
})
/* checkedRow */
const handleCheckedRow = () => {
  const { checkedRow, data, rowKey } = props
  if (!checkedRow || !rowKey) return
  const row = data.find((it) => it[rowKey] === checkedRow)
  if (!row) return
  gridRef.value?.setCurrentRow(row)
}
onMounted(() => {
  handleCheckedRow()
})
watch(() => props.checkedRow, handleCheckedRow)
</script>
