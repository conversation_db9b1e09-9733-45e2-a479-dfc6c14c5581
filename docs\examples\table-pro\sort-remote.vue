<template>
  <bs-table-pro sort-remote :columns="tableColumns" :data="tableData" @sort-change="handleSortChange" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const tableColumns = [
  {
    label: 'ID',
    value: 'id',
    width: 60,
    sortable: true
  },
  {
    label: '姓名',
    value: 'name',
    width: 180
  },
  {
    label: '年龄',
    value: 'age',
    width: 80,
    sortable: true
  },
  {
    label: '日期',
    value: 'date',
    with: 180
  },
  {
    label: '地址',
    value: 'address'
  }
]
const rawData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  }
]
const tableData = ref<any[]>(rawData)
const handleSortChange = ({ field, order }: any) => {
  rawData.sort((a, b) => {
    return order === 'desc' ? a[field] - b[field] : b[field] - a[field]
  })
  tableData.value = [...rawData]
}
</script>
