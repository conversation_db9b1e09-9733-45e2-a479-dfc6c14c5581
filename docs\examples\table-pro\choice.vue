<template>
  <bs-table-pro
    ref="tableRef"
    row-key="id"
    :operations="operations"
    :data="tableData"
    highlight-current-row
    :columns="tableColumns"
    :checked-row="checkedRow"
    @current-change="handleCurrentChange"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const tableRef = ref()
const checkedRow = ref<string | number>(1)
const tableColumns = [
  {
    label: '姓名',
    value: 'name',
    with: 180
  },
  {
    label: '年龄',
    value: 'age',
    width: 80
  },
  {
    label: '日期',
    value: 'date',
    with: 180
  },
  {
    label: '地址',
    value: 'address'
  }
]
const tableData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  }
]
const operations = [
  {
    key: 'methods',
    content: '通过方法修改当前选中',
    handler: () => {
      checkedRow.value = ''
      tableRef.value?.setCurrentRow(tableData[1])
    }
  },
  {
    key: 'props',
    content: '通过props修改当前选中',
    handler: () => {
      tableRef.value?.clearCurrentRow()
      checkedRow.value = 3
    }
  },
  {
    key: 'clear',
    content: '通过方法清除当前选中',
    handler: () => {
      checkedRow.value = ''
      tableRef.value?.clearCurrentRow()
    }
  }
]
const handleCurrentChange = (newRow, oldRow) => {
  console.log('checked-result: newRow', newRow)
  console.log('checked-result: oldRow', oldRow)
}
</script>
