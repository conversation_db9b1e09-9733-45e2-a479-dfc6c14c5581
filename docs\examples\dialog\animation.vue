<template>
  <div>
    <h2>调试动画禁用功能</h2>

    <div style="margin-bottom: 20px">
      <el-button @click="showDialog = true">显示对话框</el-button>
      <el-checkbox v-model="disableAnimation" style="margin-left: 20px"> 禁用动画 </el-checkbox>
    </div>

    <p>当前动画状态: {{ disableAnimation ? '禁用' : '启用' }}</p>
    <p style="color: #666; font-size: 14px">提示：勾选"禁用动画"后，对话框应该立即显示/隐藏，没有渐入渐出效果。</p>

    <!-- 测试对话框 -->
    <BsDialog v-model="showDialog" title="测试对话框" size="small" :disable-animation="disableAnimation" @ok="handleOk" @cancel="handleCancel">
      <p>这是一个测试对话框。</p>
      <p>动画状态: {{ disableAnimation ? '禁用' : '启用' }}</p>
      <p>请观察对话框的弹出和关闭动画效果。</p>
    </BsDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElButton, ElCheckbox } from 'element-plus'
import { BsDialog } from '@bs-nexus/components/dialog'

const showDialog = ref(false)
const disableAnimation = ref(false)

const handleOk = () => {
  showDialog.value = false
}

const handleCancel = () => {
  showDialog.value = false
}
</script>
