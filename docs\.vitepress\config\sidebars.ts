import ElComponent<PERSON><PERSON> from '../component.json'

const guideJson = {
  "basic": {
    "text": "基础",
    "children": [
      
      {
        "link": "/installation",
        "text": "安装"
      },
      {
        "link": "/quickstart",
        "text": "快速开始"
      },
      {
        "link": "/changelog",
        "text": "更新日志"
      },
    ]
  },
  "advanced":{
    "text": "进阶",
    "children": [
      {
        "link": "/theme",
        "text": "主题"
      },
      {
        "link": "/i18n",
        "text": "国际化"
      }
    ]
  },
  "dev": {
    "text": "开发",
    "children": [{
      "link": "/dev-guide",
      "text": "开发指南"
    },
    {
      "link": "/dev-standard",
      "text": "开发规范"
    }]
  }
}

function mapSideBarPrefix(data, Prefix) {
  return Object.values(data).map((item: any) => mapPrefix(item, '', Prefix))
}
// return sidebar with language configs.
// this might create duplicated data but the overhead is ignorable
const getSidebars = () => {
  return {
    '/guide/': mapSideBarPrefix(guideJson, '/guide'),
    '/component/': mapSideBarPrefix(ElComponentJson, '/component')
  }
}

type Item = {
  text: string
  children?: Item[]
  link?: string
}

function mapPrefix(item: Item, lang: string = '', prefix = '') {
  if (item.children && item.children.length > 0) {
    return {
      ...item,
      children: item.children.map((child) => mapPrefix(child, lang, prefix)),
    }
  }
  return {
    ...item,
    link: `${prefix}${item.link}`,
  }
}

export const sidebars = getSidebars()
