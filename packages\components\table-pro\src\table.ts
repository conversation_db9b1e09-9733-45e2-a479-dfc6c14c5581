import { buildProps, definePropType } from '@bs-nexus/utils'
import type { ExtractPropTypes, PropType } from 'vue'
import type Table from './table.vue'
import type { Column, MergeCell, FilterMethod, SortMethod, Tool } from './type'
import type { PaginationProps } from '@bs-nexus/components/element-plus/pagination'

export const tableProps = buildProps({
  /**
   * @description table data
   * */
  data: {
    type: definePropType<any[]>(Array),
    default: () => []
  },
  /**
   * @description table columns
   * */
  columns: {
    type: definePropType<Column[]>(Array),
    default: () => []
  },
  /**
   * @description resizable
   */
  resizable: {
    type: Boolean,
    default: true
  },
  /**
   * @description table height
   * */
  height: {
    type: definePropType([Number, String]),
    default: ''
  },
  /**
   * @description table minHeight
   * */
  minHeight: {
    type: definePropType([Number, String]),
    default: ''
  },
  /**
   * @description table maxHeight
   * */
  maxHeight: {
    type: definePropType([Number, String]),
    default: ''
  },
  /**
   * @description table syncResize
   * */
  syncResize: {
    type: definePropType([Boolean, Number, String]),
    default: ''
  },
  /**
   * @description table stripe
   */
  stripe: {
    type: Boolean,
    default: false
  },
  /**
   * @description table border
   */
  border: {
    type: Boolean,
    default: false
  },
  /**
   * @description table loading
   */
  loading: {
    type: Boolean,
    default: false
  },
  /**
   * @description table showHeader
   */
  showHeader: {
    type: Boolean,
    default: true
  },
  /**
   * @description table showHeader
   */
  showFooter: {
    type: Boolean,
    default: false
  },
  /**
   * @description table footerData
   * */
  footerData: {
    type: definePropType<any[]>(Array),
    default: () => []
  },
  /**
   * @description position of table
   * */
  align: {
    type: String,
    values: ['left', 'center', 'right'],
    default: 'left'
  },
  /**
   * @description table showOverflow
   */
  showOverflow: {
    type: Boolean,
    default: true
  },
  /**
   * @description table rowClassName
   */
  rowClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table cellClassName
   */
  cellClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table headerRowClassName
   */
  headerRowClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table headerCellClassName
   */
  headerCellClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table footerRowClassName
   */
  footerRowClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table footerCellClassName
   */
  footerCellClassName: {
    type: String,
    default: ''
  },
  /**
   * @description table crossing
   */
  crossing: {
    type: Boolean,
    default: false
  },
  /**
   * @description table highlightCurrentRow
   */
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  /**
   * @description table rowKey
   */
  rowKey: {
    type: String,
    default: ''
  },
  /**
   * @description table mergeCells
   */
  mergeCells: {
    type: definePropType<MergeCell[]>(Array),
    default: () => []
  },
  /**
   * @description table indexMethod
   */
  indexMethod: {
    type: definePropType<MergeCell[]>(Array),
    default: () => []
  },
  /**
   * @description table sortRemote
   */
  sortRemote: {
    type: Boolean,
    default: false
  },
  /**
   * @description table indexMethod
   */
  sortMethod: {
    type: Function as PropType<SortMethod>
  },
  /**
   * @description table filterRemote
   */
  filterRemote: {
    type: Boolean,
    default: false
  },
  /**
   * @description table indexMethod
   */
  filterMethod: {
    type: Function as PropType<FilterMethod>
  },
  /**
   * @description table emptyText
   */
  emptyText: {
    type: String,
    default: ''
  },
  /**
   * @description table loadingText
   */
  loadingText: {
    type: String,
    default: ''
  },
  /**
   * @description table autoVirtual
   * */
  autoVirtual: {
    type: definePropType([Boolean, Number]),
    default: 30
  },
  /**
   * @description table showToolbar
   */
  showToolbar: {
    type: Boolean,
    default: true
  },
  /**
   * @description table indexMethod
   */
  operations: {
    type: definePropType<Tool[]>(Array),
    default: () => []
  },
  /**
   * @description table pagingFront
   */
  pagingFront: {
    type: Boolean,
    default: false
  },
  /**
   * @description table pageData
   */
  pageData: {
    type: Object as PropType<Partial<PaginationProps>>,
    default: null
  },
  /**
   * @description table treeConfig
   */
  treeConfig: {
    type: Boolean,
    default: false
  },
  /**
   * @description table checkedRow
   */
  checkedRow: {
    type: definePropType([String, Number]),
    default: ''
  },
  /**
   * @description table checkedRows
   */
  checkedRows: {
    type: definePropType<string[]>(Array),
    default: () => []
  },
  /**
   * @description table expandedAll
   */
  expandedAll: {
    type: Boolean,
    default: false
  },
  /**
   * @description table expandedRows
   */
  expandedRows: {
    type: definePropType<string[]>(Array),
    default: () => []
  }
} as const)
export type TableProps = ExtractPropTypes<typeof tableProps>

export const tableEmits = [
  'refresh',
  'sort-change',
  'filter-change',
  'page-change',
  'cell-click',
  'cell-dblclick',
  'row-click',
  'row-dblclick',
  'header-click',
  'header-dblclick',
  'footer-click',
  'footer-dblclick',
  'current-change',
  'row-expand-change',
  'tree-expand-change',
  'toolbar-click',
  'scroll',
  'scroll-boundary',
  'checkbox-change',
  'checkbox-all',
  'radio-change'
]
export type TableEmits = typeof tableEmits

export type TableInstance = InstanceType<typeof Table> & unknown
