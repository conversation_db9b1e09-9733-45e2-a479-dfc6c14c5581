<template>
  <bs-switch v-model="bySelf" active-text="单独处理" inactive-text="统一处理" />
  <bs-table-pro
    height="300"
    :data="tableData"
    :columns="tableColumns"
    :filter-method="!bySelf ? handleFilterMethod : null"
    @filter-change="handleFilterChange"
  />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

const bySelf = ref(true)
const tableColumns = computed(() => {
  return [
    {
      label: '姓名',
      value: 'name',
      with: 180,
      filters: [
        {
          label: '王小虎',
          value: '王小虎'
        },
        {
          label: '王小龙',
          value: '王小龙'
        },
        {
          label: '王小二',
          value: '王小二'
        },
        {
          label: '王大锤',
          value: '王大锤'
        }
      ],
      ...(bySelf.value && { filterMethod: ({ value, row }) => value === row.name })
    },
    {
      label: '年龄',
      value: 'age',
      width: 80,
      filters: [
        {
          label: '18',
          value: 18
        },
        {
          label: '19',
          value: 19
        },
        {
          label: '20',
          value: 20
        },
        {
          label: '21',
          value: 21
        }
      ],
      filterMultiple: false
    },
    {
      label: '日期',
      value: 'date',
      with: 180,
      filters: [
        {
          label: '2016-05-02',
          value: '2016-05-02'
        },
        {
          label: '2016-05-04',
          value: '2016-05-04'
        },
        {
          label: '2016-05-01',
          value: '2016-05-01'
        },
        {
          label: '2016-05-03',
          value: '2016-05-03'
        }
      ],
      filterMultiple: false,
      ...(bySelf.value && { filterMethod: ({ value, row }) => value === row.date })
    },
    {
      label: '地址',
      value: 'address'
    }
  ]
})
const tableData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  }
]
const handleFilterMethod = ({ values, cellValue }) => values.includes(cellValue)
const handleFilterChange = (result: any) => {
  console.log('filter-change-result:', result)
}
</script>
