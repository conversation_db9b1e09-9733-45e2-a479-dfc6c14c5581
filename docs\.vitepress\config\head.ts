import fs from 'fs'
import path, { resolve } from 'path'

import type { HeadConfig } from 'vitepress'
const  docRoot = resolve(__dirname, 'docs')
const vpRoot = resolve(docRoot, '.vitepress')
export const head: HeadConfig[] = [
  [
    'link',
    {
      rel: 'icon',
      href: '/logo.png'
    },
  ],
  
  [
    'meta',
    {
      name: 'theme-color',
      content: '#ffffff',
    },
  ],
  [
    'meta',
    {
      name: 'msapplication-TileColor',
      content: '#409eff',
    },
  ],
  [
    'meta',
    {
      name: 'msapplication-config',
      content: '/browserconfig.xml',
    },
  ],
  [
    'meta',
    {
      property: 'og:image',
      content: '/images/element-plus-og-image.png',
    },
  ],
  [
    'meta',
    {
      property: 'og:image:width',
      content: '1200',
    },
  ],
  [
    'meta',
    {
      property: 'og:image:height',
      content: '630',
    },
  ],
  [
    'meta',
    {
      property: 'og:description',
      content: 'A Vue 3 based component library for designers and developers',
    },
  ],
  [
    'script',
    {
      async: 'true',
      src: 'https://www.googletagmanager.com/gtag/js?id=UA-175337989-1',
    },
  ]
]
