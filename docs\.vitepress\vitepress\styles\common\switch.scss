@use '../mixins' as *;

.switch {
  margin: 0;
  display: inline-block;
  position: relative;
  width: 40px;
  height: 20px;
  border: 1px solid var(--border-color);
  outline: none;
  border-radius: 10px;
  box-sizing: border-box;
  background: var(--bg-color-mute);
  cursor: pointer;
  transition: border-color var(--el-transition-duration),
    background-color var(--el-transition-duration);

  &:hover {
    border-color: var(--border-color-light);
  }

  &__action {
    position: absolute;
    top: 1px;
    left: 1px;
    border-radius: 50%;
    background-color: var(--bg-color);
    transform: translateX(0);
    color: var(--text-color-light);
    transition: border-color var(--el-transition-duration),
      background-color var(--el-transition-duration),
      transform var(--el-transition-duration);

    .dark & {
      transform: translateX(20px);
    }
  }

  &__icon {
    position: relative;

    .el-icon {
      position: absolute;
      left: 1px;
      bottom: 1px;
    }
  }

  &__action,
  &__icon {
    width: 16px;
    height: 16px;
  }

  &:focus-visible {
    outline: -webkit-focus-ring-color auto 1px;
    outline-offset: 1px;
  }
}
