<script lang="ts" setup>
import { isDark } from '~/composables/dark'
</script>

<template>
  <svg
    width="120"
    height="120"
    viewBox="0 0 120 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="5"
      y="16"
      width="110"
      height="84"
      rx="8"
      :fill="isDark ? '#262727' : '#F5F7FA'"
    />
    <rect
      x="10"
      y="40"
      width="100"
      height="55"
      rx="4"
      :fill="isDark ? '#141414' : 'white'"
    />
    <path
      d="M22.5 57C22.5 55.067 24.067 53.5 26 53.5H93C94.933 53.5 96.5 55.067 96.5 57V77C96.5 78.933 94.933 80.5 93 80.5H26C24.067 80.5 22.5 78.933 22.5 77V57Z"
      :fill="isDark ? '#262727' : '#ECF5FF'"
    />
    <path
      d="M22.5 57C22.5 55.067 24.067 53.5 26 53.5H93C94.933 53.5 96.5 55.067 96.5 57V77C96.5 78.933 94.933 80.5 93 80.5H26C24.067 80.5 22.5 78.933 22.5 77V57Z"
      :stroke="isDark ? '#213D5B' : '#C6E2FF'"
    />
    <path
      d="M40.432 65.1108H39.1025C39.0513 64.8267 38.9561 64.5767 38.8169 64.3608C38.6777 64.1449 38.5073 63.9616 38.3056 63.8111C38.1039 63.6605 37.878 63.5469 37.628 63.4702C37.3809 63.3935 37.1181 63.3551 36.8397 63.3551C36.3368 63.3551 35.8865 63.4815 35.4888 63.7344C35.0939 63.9872 34.7814 64.358 34.5513 64.8466C34.324 65.3352 34.2104 65.9318 34.2104 66.6364C34.2104 67.3466 34.324 67.946 34.5513 68.4347C34.7814 68.9233 35.0953 69.2926 35.4931 69.5426C35.8908 69.7926 36.3382 69.9176 36.8354 69.9176C37.111 69.9176 37.3723 69.8807 37.6195 69.8068C37.8695 69.7301 38.0953 69.6179 38.2971 69.4702C38.4988 69.3224 38.6692 69.142 38.8084 68.929C38.9505 68.7131 39.0485 68.4659 39.1025 68.1875L40.432 68.1918C40.361 68.6207 40.2232 69.0156 40.0186 69.3764C39.8169 69.7344 39.557 70.044 39.2388 70.3054C38.9235 70.5639 38.5627 70.7642 38.1564 70.9062C37.7502 71.0483 37.307 71.1193 36.8269 71.1193C36.0712 71.1193 35.3979 70.9403 34.807 70.5824C34.2161 70.2216 33.7502 69.706 33.4093 69.0355C33.0712 68.3651 32.9022 67.5653 32.9022 66.6364C32.9022 65.7045 33.0726 64.9048 33.4135 64.2372C33.7544 63.5668 34.2203 63.0526 34.8113 62.6946C35.4022 62.3338 36.074 62.1534 36.8269 62.1534C37.29 62.1534 37.7218 62.2202 38.1223 62.3537C38.5257 62.4844 38.888 62.6776 39.209 62.9332C39.53 63.1861 39.7956 63.4957 40.0059 63.8622C40.2161 64.2259 40.3581 64.642 40.432 65.1108ZM44.6625 71.1321C44.0488 71.1321 43.5133 70.9915 43.0559 70.7102C42.5985 70.429 42.2434 70.0355 41.9906 69.5298C41.7377 69.0241 41.6113 68.4332 41.6113 67.7571C41.6113 67.0781 41.7377 66.4844 41.9906 65.9759C42.2434 65.4673 42.5985 65.0724 43.0559 64.7912C43.5133 64.5099 44.0488 64.3693 44.6625 64.3693C45.2761 64.3693 45.8116 64.5099 46.269 64.7912C46.7264 65.0724 47.0815 65.4673 47.3343 65.9759C47.5872 66.4844 47.7136 67.0781 47.7136 67.7571C47.7136 68.4332 47.5872 69.0241 47.3343 69.5298C47.0815 70.0355 46.7264 70.429 46.269 70.7102C45.8116 70.9915 45.2761 71.1321 44.6625 71.1321ZM44.6667 70.0625C45.0645 70.0625 45.394 69.9574 45.6554 69.7472C45.9167 69.5369 46.1099 69.2571 46.2349 68.9077C46.3627 68.5582 46.4267 68.1733 46.4267 67.7528C46.4267 67.3352 46.3627 66.9517 46.2349 66.6023C46.1099 66.25 45.9167 65.9673 45.6554 65.7543C45.394 65.5412 45.0645 65.4347 44.6667 65.4347C44.2662 65.4347 43.9338 65.5412 43.6696 65.7543C43.4082 65.9673 43.2136 66.25 43.0858 66.6023C42.9608 66.9517 42.8983 67.3352 42.8983 67.7528C42.8983 68.1733 42.9608 68.5582 43.0858 68.9077C43.2136 69.2571 43.4082 69.5369 43.6696 69.7472C43.9338 69.9574 44.2662 70.0625 44.6667 70.0625ZM49.1358 73.4545V64.4545H50.3801V65.5156H50.4867C50.5605 65.3793 50.6671 65.2216 50.8063 65.0426C50.9455 64.8636 51.1387 64.7074 51.3858 64.5739C51.633 64.4375 51.9597 64.3693 52.3659 64.3693C52.8944 64.3693 53.3659 64.5028 53.7807 64.7699C54.1955 65.0369 54.5208 65.4219 54.7566 65.9247C54.9952 66.4276 55.1145 67.0327 55.1145 67.7401C55.1145 68.4474 54.9966 69.054 54.7608 69.5597C54.525 70.0625 54.2012 70.4503 53.7892 70.723C53.3773 70.9929 52.9071 71.1278 52.3787 71.1278C51.981 71.1278 51.6557 71.0611 51.4029 70.9276C51.1529 70.794 50.9569 70.6378 50.8148 70.4588C50.6728 70.2798 50.5634 70.1207 50.4867 69.9815H50.41V73.4545H49.1358ZM50.3844 67.7273C50.3844 68.1875 50.4512 68.5909 50.5847 68.9375C50.7182 69.2841 50.9114 69.5554 51.1642 69.7514C51.4171 69.9446 51.7267 70.0412 52.0932 70.0412C52.4739 70.0412 52.7921 69.9403 53.0478 69.7386C53.3034 69.5341 53.4966 69.2571 53.6273 68.9077C53.7608 68.5582 53.8276 68.1648 53.8276 67.7273C53.8276 67.2955 53.7623 66.9077 53.6316 66.5639C53.5037 66.2202 53.3105 65.9489 53.052 65.75C52.7963 65.5511 52.4767 65.4517 52.0932 65.4517C51.7239 65.4517 51.4114 65.5469 51.1557 65.7372C50.9029 65.9276 50.7111 66.1932 50.5804 66.5341C50.4498 66.875 50.3844 67.2727 50.3844 67.7273ZM57.1248 73.4545C56.9345 73.4545 56.7612 73.4389 56.6049 73.4077C56.4487 73.3793 56.3322 73.348 56.2555 73.3139L56.5623 72.2699C56.7953 72.3324 57.0027 72.3594 57.1845 72.3509C57.3663 72.3423 57.5268 72.2741 57.666 72.1463C57.8081 72.0185 57.9331 71.8097 58.041 71.5199L58.1987 71.0852L55.8038 64.4545H57.1674L58.8251 69.5341H58.8933L60.551 64.4545H61.9189L59.2214 71.8736C59.0964 72.2145 58.9373 72.5028 58.7441 72.7386C58.551 72.9773 58.3208 73.1563 58.0538 73.2756C57.7868 73.3949 57.4771 73.4545 57.1248 73.4545ZM66.3944 62.2727H67.9924L70.7708 69.0568H70.873L73.6515 62.2727H75.2495V71H73.9966V64.6847H73.9157L71.3418 70.9872H70.302L67.7282 64.6804H67.6472V71H66.3944V62.2727ZM79.8826 71.1321C79.2377 71.1321 78.6824 70.9943 78.2164 70.7188C77.7534 70.4403 77.3954 70.0497 77.1426 69.5469C76.8926 69.0412 76.7676 68.4489 76.7676 67.7699C76.7676 67.0994 76.8926 66.5085 77.1426 65.9972C77.3954 65.4858 77.7477 65.0866 78.1994 64.7997C78.6539 64.5128 79.1852 64.3693 79.7931 64.3693C80.1625 64.3693 80.5204 64.4304 80.867 64.5526C81.2136 64.6747 81.5247 64.8665 81.8002 65.1278C82.0758 65.3892 82.2931 65.7287 82.4522 66.1463C82.6113 66.5611 82.6909 67.0653 82.6909 67.6591V68.1108H77.4877V67.1562H81.4423C81.4423 66.821 81.3741 66.5241 81.2377 66.2656C81.1014 66.0043 80.9096 65.7983 80.6625 65.6477C80.4181 65.4972 80.1312 65.4219 79.8017 65.4219C79.4437 65.4219 79.1312 65.5099 78.8642 65.6861C78.6 65.8594 78.3954 66.0866 78.2505 66.3679C78.1085 66.6463 78.0375 66.9489 78.0375 67.2756V68.0213C78.0375 68.4588 78.1142 68.831 78.2676 69.1378C78.4238 69.4446 78.6412 69.679 78.9196 69.8409C79.198 70 79.5233 70.0795 79.8954 70.0795C80.1369 70.0795 80.3571 70.0455 80.5559 69.9773C80.7548 69.9062 80.9267 69.8011 81.0716 69.6619C81.2164 69.5227 81.3272 69.3509 81.4039 69.1463L82.6099 69.3636C82.5133 69.7187 82.34 70.0298 82.09 70.2969C81.8429 70.5611 81.5318 70.767 81.1568 70.9148C80.7846 71.0597 80.3599 71.1321 79.8826 71.1321ZM85.7111 62.2727L85.6003 68.4901H84.4199L84.3091 62.2727H85.7111ZM85.0123 71.081C84.7765 71.081 84.5748 70.9986 84.4071 70.8338C84.2395 70.6662 84.1571 70.4645 84.16 70.2287C84.1571 69.9957 84.2395 69.7969 84.4071 69.6321C84.5748 69.4645 84.7765 69.3807 85.0123 69.3807C85.2424 69.3807 85.4412 69.4645 85.6088 69.6321C85.7765 69.7969 85.8617 69.9957 85.8645 70.2287C85.8617 70.3849 85.8205 70.5284 85.7409 70.6591C85.6642 70.7869 85.562 70.8892 85.4341 70.9659C85.3063 71.0426 85.1657 71.081 85.0123 71.081Z"
      fill="#409EFF"
    />
    <rect
      x="21.5"
      y="52.5"
      width="76"
      height="29"
      stroke="var(--el-color-primary)"
    />
    <rect
      x="19.5"
      y="50.5"
      width="5"
      height="5"
      :fill="isDark ? '#141414' : 'white'"
      stroke="#409EFF"
    />
    <rect
      x="19.5"
      y="78.5"
      width="5"
      height="5"
      :fill="isDark ? '#141414' : 'white'"
      stroke="#409EFF"
    />
    <rect
      x="94.5"
      y="50.5"
      width="5"
      height="5"
      :fill="isDark ? '#141414' : 'white'"
      stroke="#409EFF"
    />
    <rect
      x="94.5"
      y="78.5"
      width="5"
      height="5"
      :fill="isDark ? '#141414' : 'white'"
      stroke="#409EFF"
    />
    <g filter="url(#filter0_d_69_6059)">
      <path
        d="M88.5822 80.6676C88.9318 80.802 88.9718 81.2807 88.6492 81.4712L84.7685 83.7634C84.7034 83.8018 84.6491 83.8561 84.6106 83.9212L82.3185 87.802C82.128 88.1245 81.6492 88.0846 81.5149 87.7349L77.4644 77.1958C77.3252 76.8337 77.6809 76.4779 78.043 76.6171L88.5822 80.6676Z"
        fill="black"
      />
      <path
        d="M88.877 81.8568C89.5221 81.4758 89.4421 80.5184 88.7428 80.2496L78.2037 76.1991C77.4795 75.9208 76.768 76.6322 77.0464 77.3564L81.0968 87.8956C81.3656 88.5949 82.323 88.6748 82.704 88.0297L82.3185 87.802L82.704 88.0297L84.9962 84.149L88.877 81.8568Z"
        stroke="white"
        stroke-width="0.895641"
      />
    </g>
    <rect
      x="51"
      y="20"
      width="16"
      height="3"
      rx="1.5"
      :fill="isDark ? '#5E5877' : '#E0D8FF'"
    />
    <path
      d="M21 30V29.2C21 28.0799 21 27.5198 21.218 27.092C21.4097 26.7157 21.7157 26.4097 22.092 26.218C22.5198 26 23.0799 26 24.2 26H93.8C94.9201 26 95.4802 26 95.908 26.218C96.2843 26.4097 96.5903 26.7157 96.782 27.092C97 27.5198 97 28.0799 97 29.2V30"
      stroke="#9480E5"
    />
    <path
      d="M13.6012 33.1012L12.5149 34.1875L11.4137 33.1012L12.5149 32L13.6012 33.1012ZM15 34.5L13.9137 35.6012L12.8274 34.5L13.9137 33.4137L15 34.5ZM12.2024 34.5L11.1012 35.6012L10 34.5L11.1012 33.4137L12.2024 34.5ZM13.6012 35.9137L12.5149 37L11.4137 35.9137L12.5149 34.8125L13.6012 35.9137Z"
      fill="#9480E5"
    />
    <rect
      x="17"
      y="33"
      width="16"
      height="3"
      rx="1.5"
      :fill="isDark ? '#5E5877' : '#E0D8FF'"
    />
    <path
      d="M98.0007 33.6868C99.9534 32.7205 101.446 31.0225 102.154 28.9619C102.199 28.8373 102.935 33.0215 105.415 33.8284C105.415 33.8284 102.414 35.2665 101.704 38.1145C101.706 38.1541 101.534 35.8525 98.0007 33.6868Z"
      fill="#FFD735"
    />
    <path
      d="M102.329 47.2387C103.937 46.442 105.164 45.0416 105.743 43.3433C105.78 43.2414 106.383 46.6838 108.433 47.3463C108.433 47.3463 105.967 48.5296 105.381 50.8709C105.373 50.902 105.231 49.0109 102.329 47.2387Z"
      fill="#FFD735"
    />
    <path
      d="M89.3746 42.0071C90.9265 41.2367 92.1124 39.886 92.6756 38.2475C92.7095 38.1456 93.2955 41.4692 95.2744 42.109C95.2744 42.109 92.8907 43.2414 92.3274 45.5062C92.3132 45.543 92.1773 43.717 89.3746 42.0071Z"
      fill="#FFD735"
    />
    <defs>
      <filter
        id="filter0_d_69_6059"
        x="71.1619"
        y="70.3146"
        width="23.9771"
        height="23.9771"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="2.68692" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_69_6059"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_69_6059"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
</template>
