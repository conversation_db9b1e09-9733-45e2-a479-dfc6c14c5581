<script>
import ElDropdown from 'element-ui/packages/dropdown'
import ElDropdownMenu from 'element-ui/packages/dropdown-menu'
import ElDropdownItem from 'element-ui/packages/dropdown-item'
import ElAutocomplete from 'element-ui/packages/autocomplete'
import ElInput from 'element-ui/packages/input'
import Locale from 'element-ui/src/mixins/locale'

export default {
  name: 'BsSearch',
  components: { ElDropdown, ElDropdownMenu, ElDropdownItem, ElAutocomplete, ElInput },
  mixins: [Locale],
  props: {
    value: [String, Number],
    width: [String, Number],
    autoSearch: {
      type: Boolean,
      default: true
    },
    autoTrim: {
      type: Boolean,
      default: true
    },
    selectValue: String,
    options: Array,
    placeholder: String,
    loadData: Function,
    autoClearValue: {
      default: true,
      type: Boolean
    },
    popperClass: String
  },
  data() {
    return {
      attrs: {},
      innerValue: '',
      innerSelectValue: '', // needSuggestions为true时，返回完整的数据
      completeValue: null,
      selectVisible: false
    }
  },
  computed: {
    boxWidth() {
      const { width, showSelect } = this
      return (this.isNumber(width) ? `${width}px` : width) || (showSelect ? '290px' : '210px')
    },
    showSelect() {
      return this.options && Array.isArray(this.options)
    },
    selectText() {
      const target = this.options.find((e) => e.value === this.innerSelectValue)
      return target ? target.label : ''
    },
    needSuggestions() {
      if (!this.showSelect) return false
      const option = (this.options || []).find((item) => item.value === this.innerSelectValue)
      return option && option.type === 'autocomplete'
    }
  },
  watch: {
    value(newVal) {
      this.innerValue = newVal
    },
    selectValue(newVal) {
      this.innerSelectValue = newVal
      this.autoSearch && this.emitSearch()
    }
  },
  created() {
    this.innerValue = this.value
    // this.innerSelectValue = this.selectValue || (this.showSelect && this.options.length ? this.options[0].value : '');
    // 不自动选中第一项
    this.innerSelectValue = this.selectValue
    this.resetAttrs()
  },
  methods: {
    handleInput(val) {
      this.$emit('input', val)
      this.debounce(this.emitChange)()
      if (this.autoSearch) {
        this.debounce(this.emitSearch, val)()
      }
    },
    handleChange() {
      this.completeValue = null
      this.emitChange()
    },
    debounce(fn, args, delay = 500) {
      const _this = this
      return function () {
        fn.timerId && clearInterval(fn.timerId)
        fn.timerId = setTimeout(() => {
          fn.call(_this, args)
        }, delay)
      }
    },
    handleIconClick() {
      this.needSuggestions && this.$refs.elInput.close()
      this.emitSearch(this.innerValue)
    },
    handleCommand(val) {
      this.selectVisible = false
      this.innerSelectValue = val
      // 重置innerValue
      this.autoClearValue && (this.innerValue = '')
      this.$emit('input', this.innerValue)
      this.emitChange()
      this.autoSearch && this.emitSearch()
    },
    handleVisibleChange(val) {
      this.selectVisible = val
    },
    handleSelect(val) {
      this.completeValue = val
    },
    emitSearch(val) {
      val = val || this.innerValue
      if (this.autoTrim) {
        val = val.trim()
      }
      const data = this.showSelect ? { [this.innerSelectValue]: val } : val
      this.$emit('search', data, this.completeValue)
    },
    emitChange() {
      const data = this.showSelect ? { [this.innerSelectValue]: this.innerValue } : this.innerValue
      this.$emit('change', data, this.completeValue)
    },
    resetAttrs() {
      this.attrs = Object.assign({}, this.$attrs, {
        type: 'text',
        showWordLimit: false,
        clearable: false,
        showPassword: false,
        disabled: false,
        prefixIcon: '',
        suffixIcon: ''
      })
    },
    isNumber(num) {
      return typeof num === 'number' || /^[1-9][0-9]*$/.test(num)
    },
    getPrependDom() {
      return (
        <el-dropdown
          slot="prepend"
          trigger="click"
          class="bs-search__select"
          placement="bottom-start"
          on-command={this.handleCommand}
          on-visible-change={this.handleVisibleChange}
        >
          <div class={['bs-search__select-target', this.selectVisible ? 'bs-search__select-active-target' : '']}>
            <span class={['bs-search__select-text', this.selectText ? '' : 'bs-search-no-text']}>
              {this.selectText ? this.selectText : this.t('bsEl.common.placeholder2')}
            </span>
            <i class={['el-icon--right', 'bs-search__select-icon', 'el-icon-arrow-up', this.selectVisible ? 'is-reverse' : '']}></i>
          </div>
          <el-dropdown-menu slot="dropdown" ref="dropdownMenu" class={this.popperClass}>
            {this.options.map((item) => (
              <el-dropdown-item
                key={item.value}
                command={item.value}
                class={['bs-search__select-item', this.innerSelectValue === item.value ? 'bs-search__select-item-active' : '']}
              >
                {item.label}
              </el-dropdown-item>
            ))}
          </el-dropdown-menu>
        </el-dropdown>
      )
    },
    getSuffixDom() {
      return (
        <div slot="suffix" onClick={this.handleIconClick} class="bs-search--suffix">
          <i class="el-input__icon bs-icon-sousuo" />
        </div>
      )
    }
  },
  render(h) {
    return (
      <div class="bs-search" style={{ width: this.boxWidth }}>
        {this.needSuggestions ? (
          <el-autocomplete
            ref="elInput"
            value={this.innerValue}
            placeholder={this.placeholder}
            fetch-suggestions={this.loadData}
            on-select={this.handleSelect}
            on-input={this.handleInput}
            on-change={this.handleChange}
            {...{ props: this.$attrs }}
          >
            {this.getPrependDom()}
            {this.getSuffixDom()}
          </el-autocomplete>
        ) : (
          <el-input
            ref="elInput"
            value={this.innerValue}
            placeholder={this.placeholder}
            on-input={this.handleInput}
            on-change={this.handleChange}
            {...{ props: this.$attrs }}
          >
            {this.showSelect && this.getPrependDom()}
            {this.getSuffixDom()}
          </el-input>
        )}
      </div>
    )
  }
}
</script>
