<template>
  <bs-switch v-model="useProp" active-text="Prop" inactive-text="Slot" />
  <bs-table-pro :operations="operations" :columns="tableColumns" :data="tableData" @toolbar-click="handleToolbarClick">
    <template v-if="!useProp" #operations>
      <el-input style="width: 240px" placeholder="通过slot定义的组件" />
    </template>
  </bs-table-pro>
</template>

<script lang="ts" setup>
import { computed, inject, ref } from 'vue'

const BsMessage: any = inject('BsMessage')

const useProp = ref(true)
const customOperations = [
  {
    key: 'delete',
    content: '删除',
    handler() {
      BsMessage.success('delete is clicked')
    }
  },
  {
    key: 'import',
    content: '导入'
  },
  {
    key: 'output',
    content: '导出',
    children: [
      {
        key: 'upload',
        content: '上传',
        handler() {
          BsMessage.success('delete is clicked')
        }
      },
      {
        key: 'download',
        content: '下载'
      },
      {
        key: 'edit',
        content: '编辑'
      }
    ]
  }
]
const operations = computed(() => (useProp.value ? customOperations : []))
const handleToolbarClick = (type) => BsMessage.success(`${type} is clicked`)
const tableColumns = [
  {
    label: '姓名',
    value: 'name',
    with: 180
  },
  {
    label: '年龄',
    value: 'age',
    width: 80
  },
  {
    label: '日期',
    value: 'date',
    with: 180
  },
  {
    label: '地址',
    value: 'address'
  }
]
const tableData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  }
]
</script>
