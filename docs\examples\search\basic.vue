<template>
  <div>
    <h3>基础搜索</h3>
    <bs-search v-model="searchValue1" placeholder="请输入搜索内容" @search="handleSearch1" />
    <p>搜索值: {{ searchValue1 }}</p>

    <h3>带选择器的搜索</h3>
    <bs-search
      v-model="searchValue2"
      v-model:select-value="selectValue"
      :options="searchOptions"
      placeholder="请输入搜索内容"
      @search="handleSearch2"
      @change="handleChange"
    />
    <p>搜索值: {{ searchValue2 }}</p>
    <p>选择器值: {{ selectValue }}</p>

    <h3>自动完成搜索</h3>
    <bs-search
      v-model="searchValue3"
      v-model:select-value="selectValue2"
      :options="autocompleteOptions"
      :load-data="loadSuggestions"
      placeholder="请输入搜索内容"
      @search="handleSearch3"
    />
    <p>搜索值: {{ searchValue3 }}</p>
    <p>选择器值: {{ selectValue2 }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchValue1 = ref('')
const searchValue2 = ref('')
const searchValue3 = ref('')
const selectValue = ref('name')
const selectValue2 = ref('title')

const searchOptions = [
  { label: '姓名', value: 'name' },
  { label: '邮箱', value: 'email' },
  { label: '电话', value: 'phone' }
]

const autocompleteOptions = [
  { label: '标题', value: 'title', type: 'autocomplete' },
  { label: '内容', value: 'content' }
]

const handleSearch1 = (data: any) => {
  console.log('基础搜索:', data)
}

const handleSearch2 = (data: any) => {
  console.log('带选择器搜索:', data)
}

const handleSearch3 = (data: any, completeValue: any) => {
  console.log('自动完成搜索:', data, completeValue)
}

const handleChange = (data: any) => {
  console.log('搜索变化:', data)
}

const loadSuggestions = (queryString: string, callback: (suggestions: any[]) => void) => {
  const suggestions = [
    { value: 'Vue.js 教程' },
    { value: 'Vue 3 新特性' },
    { value: 'Vue Router 使用' },
    { value: 'Vuex 状态管理' },
    { value: 'Element Plus 组件库' }
  ].filter((item) => item.value.toLowerCase().includes(queryString.toLowerCase()))

  setTimeout(() => {
    callback(suggestions)
  }, 200)
}
</script>
