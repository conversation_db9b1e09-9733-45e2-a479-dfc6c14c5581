<script lang="ts" setup>
import { computed } from 'vue'


</script>

<template>
  <div class="cards">
    <el-card shadow="hover">
      <guide-svg w="40" m="y-12" />
      <h3>指南</h3>
      <p>了解设计指南，帮助产品设计人员搭建逻辑清晰、结构合理且高效易用的产品。</p>
      <template #footer>
        <a :href="`/guide/design.html`">查看详情</a>
      </template>
    </el-card>
    <el-card shadow="hover">
      <component-svg w="40" m="y-12" />
      <h3>组件</h3>
      <p>使用组件 Demo 快速体验交互细节；使用前端框架封装的代码帮助工程师快速开发。</p>
      <template #footer>
        <a href="https://element-plus.org/zh-CN/component/overview.html">
          查看详情
        </a>
      </template>
    </el-card>
    <el-card shadow="hover">
      <resource-svg w="40" m="y-12" />
      <h3>资源</h3>
      <p>下载相关资源，用其快速搭建页面原型或高保真视觉稿，提升产品设计效率。</p>
      <template #footer>
        <a href="https://element-plus.org/zh-CN/resource/">查看详情</a>
      </template>
    </el-card>
  </div>
</template>

<style lang="scss">
.home-page {
  .cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin: 0 auto 80px;
    max-width: 1200px;
  }

  .el-card {
    height: 430px;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    transition: all 0.3s;
    bottom: 0;

    .el-card__body {
      padding: 0;
      height: 100%;
    }

    .el-card__footer {
      position: sticky;
      bottom: 0;
      padding: 0;
    }

    img {
      margin: 48px auto;
    }

    h3 {
      margin: 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
      font-weight: normal;
    }

    p {
      font-size: 14px;
      color: #99a9bf;
      padding: 0 25px;
      line-height: 20px;
      margin: 16px 0;
    }

    a {
      line-height: 52px;
      font-size: 14px;
      color: var(--brand-color);
      text-align: center;
      border: 0;
      padding: 0;
      cursor: pointer;
      background-color: var(--bg-color);
      transition: all 0.3s;
      text-decoration: none;
      display: block;

      &:hover {
        color: #fff;
        background: var(--brand-color);
      }
    }

    &:hover {
      bottom: 6px;
    }
  }

  @media (max-width: 768px) {
    .cards {
      grid-template-columns: 1fr;
    }

    .el-card {
      height: auto;
    }
  }
}
</style>
