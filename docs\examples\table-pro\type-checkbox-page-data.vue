<template>
  <bs-table-pro crossing :columns="tableColumns" :data="tableData" :page-data="pageData" @page-change="handlePageChange" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const tableColumns = [
  {
    type: 'checkbox',
    width: 40
  },
  {
    label: '姓名',
    value: 'name',
    with: 180
  },
  {
    label: '年龄',
    value: 'age',
    width: 80
  },
  {
    label: '日期',
    value: 'date',
    with: 180
  },
  {
    label: '地址',
    value: 'address'
  }
]
const allTableData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  },
  {
    id: 5,
    date: '2016-05-05',
    name: '王小明',
    age: 22,
    address: '上海市普陀区金沙江路 1520 弄'
  },
  {
    id: 6,
    date: '2016-05-06',
    name: '王小芳',
    age: 19,
    address: '上海市普陀区金沙江路 1521 弄'
  },
  {
    id: 7,
    date: '2016-05-07',
    name: '王小强',
    age: 20,
    address: '上海市普陀区金沙江路 1522 弄'
  },
  {
    id: 8,
    date: '2016-05-08',
    name: '王小丽',
    age: 21,
    address: '上海市普陀区金沙江路 1523 弄'
  },
  {
    id: 9,
    date: '2016-05-09',
    name: '王小刚',
    age: 23,
    address: '上海市普陀区金沙江路 1524 弄'
  },
  {
    id: 10,
    date: '2016-05-10',
    name: '王小燕',
    age: 18,
    address: '上海市普陀区金沙江路 1525 弄'
  },
  {
    id: 11,
    date: '2016-05-11',
    name: '王小兵',
    age: 24,
    address: '上海市普陀区金沙江路 1526 弄'
  },
  {
    id: 12,
    date: '2016-05-12',
    name: '王小霞',
    age: 20,
    address: '上海市普陀区金沙江路 1527 弄'
  },
  {
    id: 13,
    date: '2016-05-13',
    name: '王小杰',
    age: 22,
    address: '上海市普陀区金沙江路 1528 弄'
  },
  {
    id: 14,
    date: '2016-05-14',
    name: '王小敏',
    age: 19,
    address: '上海市普陀区金沙江路 1529 弄'
  }
]
const tableData = ref<any[]>([])
const pageData = ref({
  currentPage: 1,
  pageSize: 4,
  total: 0
})
const handlePageChange = (currentPage: number, pageSize: number) => {
  pageData.value.currentPage = currentPage
  pageData.value.pageSize = pageSize
  tableData.value = allTableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
}
const init = () => {
  pageData.value.total = allTableData.length
  const { currentPage, pageSize } = pageData.value
  tableData.value = allTableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
}

init()
</script>
