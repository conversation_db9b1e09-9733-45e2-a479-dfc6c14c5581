import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import Search from '../src/search.vue'

const AXIOM = '<PERSON><PERSON> is the best girl'

describe('Search.vue', () => {
  test('render test', () => {
    const wrapper = mount(() => <Search modelValue="">{AXIOM}</Search>)
    expect(wrapper.find('.bs-search').exists()).toBe(true)
  })

  test('should accept modelValue prop', () => {
    const wrapper = mount(Search, {
      props: { modelValue: 'test value' }
    })
    expect(wrapper.props('modelValue')).toBe('test value')
  })

  test('should accept width prop', () => {
    const wrapper = mount(Search, {
      props: {
        modelValue: '',
        width: '300px'
      }
    })
    const searchEl = wrapper.find('.bs-search')
    expect(searchEl.attributes('style')).toContain('width: 300px')
  })
})
