<template>
  <div class="bs-search" :style="{ width: boxWidth }">
    <!-- 自动完成输入框 -->
    <el-autocomplete
      v-if="needSuggestions"
      ref="elInputRef"
      :model-value="innerValue"
      :placeholder="placeholder"
      :fetch-suggestions="loadData"
      v-bind="inputAttrs"
      @select="handleSelect"
      @input="handleInput"
      @change="handleChange"
    >
      <template v-if="showSelect" #prepend>
        <SearchSelect
          :options="options"
          :select-value="innerSelectValue"
          :select-visible="selectVisible"
          :popper-class="popperClass"
          @command="handleCommand"
          @visible-change="handleVisibleChange"
        />
      </template>
      <template #suffix>
        <SearchSuffix @click="handleIconClick" />
      </template>
    </el-autocomplete>

    <!-- 普通输入框 -->
    <el-input
      v-else
      ref="elInputRef"
      :model-value="innerValue"
      :placeholder="placeholder"
      v-bind="inputAttrs"
      @input="handleInput"
      @change="handleChange"
    >
      <template v-if="showSelect" #prepend>
        <SearchSelect
          :options="options"
          :select-value="innerSelectValue"
          :select-visible="selectVisible"
          :popper-class="popperClass"
          @command="handleCommand"
          @visible-change="handleVisibleChange"
        />
      </template>
      <template #suffix>
        <SearchSuffix @click="handleIconClick" />
      </template>
    </el-input>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref, useAttrs, watch } from 'vue'
import { ElAutocomplete, ElInput } from 'element-plus'
// import { useLocale } from '@bs-nexus/hooks'
import { type SearchOption, searchEmits, searchProps } from './search'
// import SearchSelect from './search-select.vue'
// import SearchSuffix from './search-suffix.vue'

defineOptions({
  name: 'BsSearch'
})

const props = defineProps(searchProps)
const emit = defineEmits(searchEmits)
const attrs = useAttrs()
// const { t } = useLocale()

// 响应式数据
const innerValue = ref<string | number>('')
const innerSelectValue = ref<string>('')
const completeValue = ref<any>(null)
const selectVisible = ref(false)
const elInputRef = ref()

// 计算属性
const boxWidth = computed(() => {
  const { width } = props
  return (isNumber(width) ? `${width}px` : width) || (showSelect.value ? '290px' : '210px')
})

const showSelect = computed(() => {
  return props.options && Array.isArray(props.options) && props.options.length > 0
})

const needSuggestions = computed(() => {
  if (!showSelect.value) return false
  const option = props.options.find((item: SearchOption) => item.value === innerSelectValue.value)
  return option && option.type === 'autocomplete'
})

const inputAttrs = computed(() => {
  return {
    ...attrs,
    type: 'text',
    showWordLimit: false,
    clearable: false,
    showPassword: false,
    disabled: false,
    prefixIcon: '',
    suffixIcon: ''
  }
})

// 工具函数
const isNumber = (num: any): boolean => {
  return typeof num === 'number' || /^[1-9][0-9]*$/.test(num)
}

const debounce = (fn: (...args: any[]) => void, delay = 500) => {
  let timerId: number
  return (...args: any[]) => {
    clearTimeout(timerId)
    timerId = window.setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

// 发射事件
const emitSearch = () => {
  let val = innerValue.value
  if (props.autoTrim && typeof val === 'string') {
    val = val.trim()
  }
  const data = showSelect.value ? { [innerSelectValue.value]: val } : val
  emit('search', data, completeValue.value)
}

const emitChange = () => {
  const data = showSelect.value ? { [innerSelectValue.value]: innerValue.value } : innerValue.value
  emit('change', data, completeValue.value)
}

// 防抖函数
const debouncedEmitChange = debounce(emitChange)
const debouncedEmitSearch = debounce(emitSearch)

// 事件处理
const handleInput = (val: string | number) => {
  innerValue.value = val
  emit('update:modelValue', val)
  debouncedEmitChange()
  if (props.autoSearch) {
    debouncedEmitSearch()
  }
}

const handleChange = () => {
  completeValue.value = null
  emitChange()
}

const handleIconClick = () => {
  if (needSuggestions.value && elInputRef.value) {
    elInputRef.value.close()
  }
  emitSearch()
}

const handleCommand = (val: string) => {
  selectVisible.value = false
  innerSelectValue.value = val
  emit('update:selectValue', val)

  // 重置innerValue
  if (props.autoClearValue) {
    innerValue.value = ''
    emit('update:modelValue', innerValue.value)
  }

  emitChange()
  if (props.autoSearch) {
    emitSearch()
  }
}

const handleVisibleChange = (val: boolean) => {
  selectVisible.value = val
}

const handleSelect = (val: any) => {
  completeValue.value = val
}

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    innerValue.value = newVal
  },
  { immediate: true }
)

watch(
  () => props.selectValue,
  (newVal) => {
    innerSelectValue.value = newVal
    if (props.autoSearch) {
      nextTick(() => {
        emitSearch()
      })
    }
  },
  { immediate: true }
)
</script>
