@import 'mixins/mixins.scss';
@import 'common/var.scss';

@include bs(search) {
  display: inline-block;
  & > .el-input > .el-input__suffix {
    right: 0;
    width: 30px;
    border-left: $--input-border;
    margin-top: 1px;
    margin-bottom: 1px;
    height: calc(100% - 2px);
    .el-input__clear {
      position: absolute;
      left: -26px;
      right: 0;
      background: #fff;
    }
  }
  & .el-input--small .el-input__icon {
    color: $--bs-color-text-placeholder;
    font-size: 12px;
    width: 30px;
  }
  &-icon {
    cursor: pointer;
    font-size: 16px;
  }
  &--suffix {
    display: inline-block;
    width: 100%;
    height: 100%;
    line-height: normal;
    cursor: pointer;
  }
  .el-input-group__prepend {
    position: relative;
    width: 90px;
    box-sizing: border-box;
    background-color: $--bs-color-background-light;
    padding: 0;
  }
  &__select {
    position: absolute;
    top: -1px;
    bottom: -1px;
    left: -1px;
    right: -1px;
  }
  &__select-target {
    display: flex;
    align-items: center;
    padding: 0 8px;
    height: calc(100% - 2px);
    border: 1px solid transparent;
    border-radius: $--border-radius-base 0 0 $--border-radius-base;
    cursor: pointer;
    box-sizing: content-box;
  }
  &__select-active-target {
    border-color: $--bs-color-primary;
  }
  &__select-target:not(&__select-active-target):hover {
    border-color: $--bs-color-text-placeholder;
  }
  &__select-item {
    box-sizing: border-box;
    min-width: 120px;
  }
  &__select-item.el-dropdown-menu__item:not(&__select-item-active):hover,
  &__select-item.el-dropdown-menu__item:focus {
    color: $--bs-color-text-primary;
    font-weight: $--bs-font-weight;
  }
  &__select-item-active,
  &__select-item-active.el-dropdown-menu__item:hover,
  &__select-item-active.el-dropdown-menu__item:focus {
    background-color: $--bs-color-background-base;
    color: $--bs-color-primary;
  }
  &__select-text {
    width: 56px;
    color: $--bs-color-text-primary;
    font-size: 13px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-top: -1px;
  }
  &-no-text {
    color: $--bs-color-text-placeholder;
  }
  &__select-icon {
    margin-top: 2px;
    color: $--bs-color-text-placeholder;
    font-size: 14px;
    transition: transform 0.3s;
    transform: rotateZ(180deg);
  }
  &__select-icon.is-reverse {
    transform: rotateZ(0deg);
  }
}
