## Search 搜索框

封装后的搜索框

:::warning
值绑定同**el-input**的用法相同。

使用 value 进行单向绑定时，应当处理 `input` 事件，并更新组件的绑定值。否则，输入框内显示的值将不会改变。
或直接使用`v-model`进行双向绑定。

不支持 `v-model` 修饰符。
:::

### 基础用法

:::demo v-model 进行双向绑定

```html
<template>
  <div>
    <bs-search v-model="value" placeholder="请输入内容"></bs-search>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        value: ''
      }
    }
  }
</script>
```

:::

:::demo value 进行单向绑定

```html
<template>
  <div>
    <bs-search :value="value" placeholder="请输入内容" @input="handleInput"></bs-search>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        value: ''
      }
    },
    methods: {
      handleInput(val) {
        this.value = val
      }
    }
  }
</script>
```

:::

### 触发搜索

:::demo 默认实时输入、enter 键及点击进行搜索, 若不需要实时输入搜索，可以通过**autoSearch**进行设置。

```html
<template>
  <div>
    <bs-search v-model="value" placeholder="请输入内容" @search="handleSearch"></bs-search>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        value: ''
      }
    },
    methods: {
      handleSearch(val) {
        console.log(val)
      }
    }
  }
</script>
```

:::

### 组合搜索 & 远程搜索

:::demo 通过配置 options 配置 select 选项。

```html
<template>
  <div>
    <bs-search
      v-model="value"
      :selectValue="selectValue"
      :placeholder="placeholder"
      :options="options"
      :loadData="loadData"
      :autoSearch="false"
      @change="handleChange"
      @search="handleSearch"
    ></bs-search>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        value: '',
        selectValue: '2',
        options: [
          {
            value: '1',
            label: '订单号',
            type: 'autocomplete'
          },
          {
            value: '2',
            label: '手机号码'
          },
          {
            value: '3',
            label: '手机号码1'
          }
        ]
      }
    },
    computed: {
      placeholder() {
        return '请输入' + this.selectValue
      }
    },
    methods: {
      handleSearch(val, val2) {
        console.log('search', this.value)
      },
      handleChange(val, val2) {
        console.log('change', this.value)
        this.selectValue = Object.keys(val)[0]
      },
      loadData(queryString, cb) {
        setTimeout(() => {
          cb(
            [
              { value: '三全鲜食（北新泾店）', address: '长宁区新渔路144号' },
              { value: 'Hot honey 首尔炸鸡（仙霞路）', address: '上海市长宁区淞虹路661号' },
              { value: '新旺角茶餐厅', address: '上海市普陀区真北路988号创邑金沙谷6号楼113' },
              { value: '泷千家(天山西路店)', address: '天山西路438号' },
              { value: '胖仙女纸杯蛋糕（上海凌空店）', address: '上海市长宁区金钟路968号1幢18号楼一层商铺18-101' },
              { value: '贡茶', address: '上海市长宁区金钟路633号' }
            ].filter((item) => item.value.includes(queryString))
          )
        }, 1000)
      }
    }
  }
</script>
```

:::

### Attributes

| 参数            | 说明                                                     | 类型            | 可选值 | 默认值 |
| --------------- | -------------------------------------------------------- | --------------- | ------ | ------ |
| value / v-model | 绑定值                                                   | String / Number | —      | —      |
| maxlength       | 原生属性，最大输入长度                                   | Number          | —      | —      |
| minlength       | 原生属性，最小输入长度                                   | Number          | —      | —      |
| placeholder     | 输入框占位文本                                           | String          | —      | —      |
| autoSearch      | 是否需要实时触发搜索                                     | Boolean         | —      | true   |
| autoTrim        | 是否需要 trim 处理                                       | Boolean         | —      | true   |
| selectValue     | select 默认值                                            | String / Number | —      | —      |
| options         | 组合搜索时，左侧 select 配置选择                         | Array           | —      | —      |
| autoClearValue  | 组合搜索时，左侧 select 选择后是否要清除 value，默认清楚 | Array           | —      | —      |

### Options

| 参数  | 说明   | 类型                   | 可选值 | 默认值  |
| ----- | ------ | ---------------------- | ------ | ------- |
| value | key 值 | String / Number        | —      | —       |
| label | 文本   | String                 | —      | —       |
| type  | 绑定值 | `autocomplete`/`input` | —      | `input` |

### Menu Events

| 事件名称 | 说明                     | 回调参数                                        |
| -------- | ------------------------ | ----------------------------------------------- |
| search   | 触发搜的回调             | input 的值，组合搜索返回 key/value 形式的数据。 |
| change   | 数值发生更改时触发的回调 | input 的值，组合搜索返回 key/value 形式的数据。 |
