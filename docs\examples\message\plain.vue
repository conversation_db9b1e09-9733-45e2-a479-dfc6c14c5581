<template>
  <el-button :plain="true" @click="open1">Success</el-button>
  <el-button :plain="true" @click="open2">Warning</el-button>
  <el-button :plain="true" @click="open3">Message</el-button>
  <el-button :plain="true" @click="open4">Error</el-button>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'

const open1 = () => {
  ElMessage({
    message: 'Congrats, this is a success message.',
    type: 'success',
    plain: true,
  })
}
const open2 = () => {
  ElMessage({
    message: 'Warning, this is a warning message.',
    type: 'warning',
    plain: true,
  })
}
const open3 = () => {
  ElMessage({
    message: 'This is a message.',
    type: 'info',
    plain: true,
  })
}
const open4 = () => {
  ElMessage({
    message: 'Oops, this is a error message.',
    type: 'error',
    plain: true,
  })
}
</script>
