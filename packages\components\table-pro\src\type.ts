import type { ButtonProps } from '@bs-nexus/components/element-plus/button'

export interface Slots {
  default?: string | ((params) => VNode[]) // 默认自定义显示内容模板
  header?: string | ((params) => VNode[]) // 自定义表头内容的模板
  footer?: string | ((params) => VNode[]) // 自定义表尾内容的模板
  title?: string | ((params) => VNode[]) // 只对 type=checkbox,radio 有效，自定义标题模板
  content?: string | ((params) => VNode[]) // 只对 type=expand 有效，自定义展开后的内容模板
  filter?: string | ((params) => VNode[]) // 自定义筛选模板
  tip?: string | ((params) => VNode[]) // 自定义筛选模板
}
export interface Filter {
  value?: string // 值
  label?: string // 标题
  checked?: boolean // 是否选中
  resetValue?: unknown // 重置数据
  data?: unknown // 额外数据
}
export interface Column {
  type?: 'index' | 'checkbox' | 'expand' | 'radio' // 列的类型; index：序号 checkbox：多选框 expand：展开行 radio：单选框
  value?: string // 列字段名
  label?: string // 列标题
  width?: number | string // 宽度
  minWidth?: number | string // 最小宽度
  fixed?: 'left' | 'right' // 将列固定在左侧或者右侧
  align?: 'left' | 'center' | 'right' // 对齐方式
  showOverflow?: boolean // 表格文字超出是否显示... 默认换行 反转
  className?: string // 单元格附加 className
  headerClassName?: string // 表头的单元格附加 className
  footerClassName?: string // 表尾的单元格附加 className
  formatter?: (({ cellValue, row, column }) => string) | any[] | string // 格式化显示内容
  sortable?: boolean // 是否排序
  sortBy?: string | (({ row, column }) => string | number) // 排序方法
  filters: Filter[] // 过滤条件集合
  filterMultiple?: boolean // 是否多选过滤条件
  filterMethod?: ({ value, option, cellValue, row, column }) => boolean // 过滤方法
  tip?: string // 提示文案 增加一个插槽
  children?: Column[] // 表头分组
  slots?: Slots // 插槽,
  resizable?: boolean // 是否允许拖动列宽调整大小
}
export interface MergeCell {
  row: number // 行位置
  col: number // 列位置
  rowspan: number // 跨几行
  colspan: number // 跨几列
}
export interface Tool extends Partial<ButtonProps> {
  key: string | number // 唯一标识
  content: string // 按钮标题
  divided?: boolean // Dropdown.divided 是否显示分隔符
  handler?: () => void // 自定义处理方式
  children?: Tool[] // 下拉菜单的内容
  visible: boolean
}

export interface SortMethodParams {
  data: any[]
  column: any[]
  field: string
  order: string
}
export interface FilterMethodParams {
  options: any[]
  values: string[]
  cellValue: string
  row: object
  column: object
}
export type SortMethod = (raw: SortMethodParams) => any[]
export type FilterMethod = (raw: FilterMethodParams) => boolean
