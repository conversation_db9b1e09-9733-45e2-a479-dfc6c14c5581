import { buildProps, definePropType } from '@bs-nexus/utils'

import type { ExtractPropTypes } from 'vue'

export interface SearchOption {
  label: string
  value: string
  type?: 'autocomplete' | 'normal'
}

export const searchProps = buildProps({
  /**
   * @description 输入框的值
   */
  modelValue: {
    type: [String, Number],
    default: ''
  },
  /**
   * @description 组件宽度
   */
  width: {
    type: [String, Number]
  },
  /**
   * @description 是否自动搜索
   */
  autoSearch: {
    type: Boolean,
    default: true
  },
  /**
   * @description 是否自动去除首尾空格
   */
  autoTrim: {
    type: Boolean,
    default: true
  },
  /**
   * @description 选择器的值
   */
  selectValue: {
    type: String,
    default: ''
  },
  /**
   * @description 选择器选项
   */
  options: {
    type: definePropType<SearchOption[]>(Array),
    default: () => []
  },
  /**
   * @description 输入框占位符
   */
  placeholder: {
    type: String,
    default: ''
  },
  /**
   * @description 自动完成数据加载函数
   */
  loadData: {
    type: definePropType<(queryString: string, callback: (suggestions: any[]) => void) => void>(Function)
  },
  /**
   * @description 切换选择器时是否自动清空输入值
   */
  autoClearValue: {
    type: Boolean,
    default: true
  },
  /**
   * @description 下拉菜单的自定义类名
   */
  popperClass: {
    type: String,
    default: ''
  }
} as const)

export type SearchProps = ExtractPropTypes<typeof searchProps>

export const searchEmits = {
  'update:modelValue': (value: string | number) => true,
  'update:selectValue': (value: string) => true,
  search: (data: any, completeValue?: any) => true,
  change: (data: any, completeValue?: any) => true
}

export type SearchEmits = typeof searchEmits
