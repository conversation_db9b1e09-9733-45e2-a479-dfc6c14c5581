import path, { resolve } from 'path'
import Inspect from 'vite-plugin-inspect'
import UnoCSS from 'unocss/vite'
import mkcert from 'vite-plugin-mkcert'
import glob from 'fast-glob'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Components from 'unplugin-vue-components/vite'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { loadEnv } from 'vitepress'
// import {
//   docPackage,
//   epPackage,
//   getPackageDependencies,
//   projRoot,
// } from '@element-plus/build-utils'
// const docPackage = resolve(__dirname, '..', '..', 'package.json');
// const epPackage = '/';
// const projRoot = '/';
const projRoot = resolve(__dirname);
import { MarkdownTransform } from '../plugins/markdown-transform'
import type { Plugin, UserConfig } from 'vitepress'

type ViteConfig = Required<UserConfig>['vite']
type ResolveOptions = Required<ViteConfig>['resolve']
type AliasOptions = Required<ResolveOptions>['alias']

// const getPackageManifest = (pkgPath: string) => {
//   // eslint-disable-next-line @typescript-eslint/no-var-requires
//   return require(pkgPath) as any
// }
// const getPackageDependencies = (
//   pkgPath: string
// ): Record<'dependencies' | 'peerDependencies', string[]> => {
//   const manifest = getPackageManifest(pkgPath)
//   const { dependencies = {}, peerDependencies = {} } = manifest

//   return {
//     dependencies: Object.keys(dependencies),
//     peerDependencies: Object.keys(peerDependencies),
//   }
// }
// const { dependencies: epDeps } = getPackageDependencies(epPackage)
// const { dependencies: docsDeps } = getPackageDependencies(docPackage)
// const optimizeDeps = [...new Set([...epDeps, ...docsDeps])].filter(
//   (dep) =>
//     !dep.startsWith('@types/') &&
//     !['@element-plus/metadata', 'element-plus'].includes(dep)
// )
// optimizeDeps.push(
//   ...(await glob(['dayjs/plugin/*.js'], {
//     cwd: path.resolve(projRoot, 'node_modules'),
//     onlyFiles: true,
//   }))
// )

const alias: AliasOptions = [
  {
    find: '~/',
    replacement: `${path.resolve(__dirname, '../vitepress')}/`,
  },
  // TODO:后续bs组件需要从本地代码引入
  // ...(process.env.DOC_ENV === 'production'
  //   ? []
  //   : [
  //       {
  //         find: /^element-plus(\/(es|lib))?$/,
  //         replacement: path.resolve(projRoot, 'packages/element-plus/index.ts'),
  //       },
  //       {
  //         find: /^element-plus\/(es|lib)\/(.*)$/,
  //         replacement: `${path.resolve(projRoot, 'packages')}/$2`,
  //       },
  //     ]),
]

export const getViteConfig = ({ mode }: { mode: string }): ViteConfig => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
    server: {
      host: true,
      fs: {
        allow: [projRoot],
      },
    },
    resolve: {
      alias,
    },
    plugins: [
      vueJsx(),

      // https://github.com/antfu/unplugin-vue-components
      Components({
        dirs: ['.vitepress/vitepress/components'],

        allowOverrides: true,

        // custom resolvers
        resolvers: [
          // auto import icons
          // https://github.com/antfu/unplugin-icons
          IconsResolver(),
        ],

        // allow auto import and register components used in markdown
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      }),

      // https://github.com/antfu/unplugin-icons
      Icons({
        autoInstall: true,
      }),
      UnoCSS(),
      MarkdownTransform(),
      Inspect(),
      env.HTTPS ? (mkcert() as Plugin) : undefined,
    ],
    optimizeDeps: {
      // include: optimizeDeps,
    },
  }
}
