<template>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column type="selection" width="55" />
    <el-table-column label="Date" width="120">
      <template #default="scope">{{ scope.row.date }}</template>
    </el-table-column>
    <el-table-column property="name" label="Name" width="120" />
    <el-table-column
      property="address"
      label="use show-overflow-tooltip"
      width="240"
      show-overflow-tooltip
    />
    <el-table-column property="address" label="address" />
  </el-table>
</template>

<script lang="ts" setup>
import { ElTable } from 'element-plus'

interface User {
  date: string
  name: string
  address: string
}
const tableData: User[] = [
  {
    date: '2016-05-04',
    name: '<PERSON><PERSON><PERSON>',
    address: 'Lohrbergstr. 86c, Süd <PERSON>li, Saarland',
  },
  {
    date: '2016-05-03',
    name: '<PERSON>',
    address: '760 A Street, South Frankfield, Illinois',
  },
  {
    date: '2016-05-02',
    name: '<PERSON>',
    address: 'Arnold-Ohletz-Str. 41a, Alt <PERSON>, Th<PERSON>ringen',
  },
  {
    date: '2016-05-01',
    name: '<PERSON><PERSON>',
    address: '23618 Windsor Drive, West Ricardoview, Idaho',
  },
]
</script>
