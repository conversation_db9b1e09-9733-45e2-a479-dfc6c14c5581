// import { uniqueId } from 'lodash-unified'
import XEUtils from 'xe-utils'
// export const getRootClass = ({ className, size, perfect, loading }) => {
//   return [
//     'vxe-toolbar',
//     className ? (XEUtils.isFunction(className) ? className({ $toolbar: bsToolbar }) : className) : '',
//     {
//       [`size--${size}`]: size,
//       'is--perfect': perfect,
//       'is--loading': loading
//     }
//   ]
// }

export const isFunction = (raw: unknown) => typeof raw === 'function'

export const uniqueId = XEUtils.uniqueId

export const handleOverflow = (column, global) => {
  if (typeof column !== 'boolean') return global ? 'tooltip' : false
  return column ? 'tooltip' : false
}

export const handleColumns = (columns, props) => {
  return columns.map((item) => {
    const it = { ...item }
    if (!['index', 'checkbox', 'expand', 'radio'].includes(it.type)) delete it.type
    if (it.type === 'index') {
      it.type = 'seq'
    }
    if (!it.type) {
      it.field = it.value
    }
    it.title = it.label
    const flag = handleOverflow(it.showOverflow, props.showOverflow)
    it.showOverflow = flag
    it.showHeaderOverflow = flag
    it.showFooterOverflow = flag
    it.treeNode = Object.keys(props.treeConfig).length
    it.minWidth = it.minWidth || it.width || 100

    it.padding = true
    if (Array.isArray(it.children)) {
      it.children = handleColumns(it.children, props)
    }
    return it
  })
}
