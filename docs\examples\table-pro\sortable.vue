<template>
  <bs-switch v-model="bySelf" active-text="单独处理" inactive-text="统一处理" />
  <bs-table-pro :columns="tableColumns" :data="tableData" :sort-method="!bySelf ? handleSort : null" @sort-change="handleSortChange" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const bySelf = ref(true)
const tableColumns = [
  {
    label: 'ID',
    value: 'id',
    width: 60,
    ...(bySelf.value && {
      sortable: true,
      sortBy: ({ row }: any) => row.name
    })
  },
  {
    label: '姓名',
    value: 'name',
    width: 180
  },
  {
    label: '年龄',
    value: 'age',
    width: 80,
    ...(bySelf.value && { sortable: true })
  },
  {
    label: '日期',
    value: 'date',
    with: 180
  },
  {
    label: '地址',
    value: 'address'
  }
]
const tableData = [
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    age: 18,
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    id: 2,
    date: '2016-05-04',
    name: '王小龙',
    age: 19,
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    id: 3,
    date: '2016-05-01',
    name: '王小二',
    age: 20,
    address: '上海市普陀区金沙江路 1519 弄'
  },
  {
    id: 4,
    date: '2016-05-03',
    name: '王大锤',
    age: 21,
    address: '上海市普陀区金沙江路 1516 弄'
  }
]
const handleSort = ({ data, sortList }) => {
  const { field, order } = sortList[0]
  if (!order) return data
  return data.sort((a, b) => {
    return order === 'desc' ? a[field] - b[field] : b[field] - a[field]
  })
}
const handleSortChange = (result: any) => {
  console.log('sort-change-result:', result)
}
</script>
