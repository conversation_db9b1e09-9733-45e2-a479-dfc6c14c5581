<template>
  <el-dropdown trigger="click" class="bs-search__select" placement="bottom-start" @command="handleCommand" @visible-change="handleVisibleChange">
    <div :class="['bs-search__select-target', selectVisible ? 'bs-search__select-active-target' : '']">
      <span :class="['bs-search__select-text', selectText ? '' : 'bs-search-no-text']">
        {{ selectText || t('bsEl.common.placeholder2') }}
      </span>
      <el-icon :class="['bs-search__select-icon', selectVisible ? 'is-reverse' : '']">
        <ArrowUp />
      </el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu :class="popperClass">
        <el-dropdown-item
          v-for="item in options"
          :key="item.value"
          :command="item.value"
          :class="['bs-search__select-item', selectValue === item.value ? 'bs-search__select-item-active' : '']"
        >
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElIcon } from 'element-plus'
import { ArrowUp } from '@element-plus/icons-vue'
import { useLocale } from '@bs-nexus/hooks'
import type { SearchOption } from './search'

defineOptions({
  name: 'BsSearchSelect'
})

interface Props {
  options: SearchOption[]
  selectValue: string
  selectVisible: boolean
  popperClass?: string
}

interface Emits {
  (e: 'command', value: string): void
  (e: 'visible-change', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useLocale()

const selectText = computed(() => {
  const target = props.options.find((e) => e.value === props.selectValue)
  return target ? target.label : ''
})

const handleCommand = (value: string) => {
  emit('command', value)
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}
</script>
