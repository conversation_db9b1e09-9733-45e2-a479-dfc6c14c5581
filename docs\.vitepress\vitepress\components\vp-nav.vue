<script setup lang="ts">
import { isClient } from '@vueuse/core'

import { useSidebar } from '../composables/sidebar'
import { breakpoints } from '../constant'
import VpNavbar from './vp-navbar.vue'
import { ref } from 'vue'

const { hasSidebar } = useSidebar()
const toggleFullScreen = ref(false)
</script>

<template>
  <header :class="{ navbar: true, 'has-sidebar': hasSidebar }">
    <VpNavbar  />
  </header>
</template>
